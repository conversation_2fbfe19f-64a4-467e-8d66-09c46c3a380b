const addFileOutline =
  require('../assets/icons/add-file-outline.png') as string;
const addFile = require('../assets/icons/add-file.png') as string;
const arrowBack = require('../assets/icons/arrow-back.png') as string;
const arrowDown = require('../assets/icons/arrow-down.png') as string;
const arrowRight = require('../assets/icons/arrow-right.png') as string;
const arrowUp = require('../assets/icons/arrow-up.png') as string;
const bag = require('../assets/icons/bag.png') as string;
const bellOutline = require('../assets/icons/bell-outline.png') as string;
const bell = require('../assets/icons/bell.png') as string;
const bookmarkOutline =
  require('../assets/icons/bookmark-outline.png') as string;
const bookmark = require('../assets/icons/bookmark.png') as string;
const calendar = require('../assets/icons/calendar.png') as string;
const calendar2 = require('../assets/icons/calendar2.png') as string;
const calendar3 = require('../assets/icons/calendar3.png') as string;
const call = require('../assets/icons/call.png') as string;
const categoryOutline =
  require('../assets/icons/category-outline.png') as string;
const category = require('../assets/icons/category.png') as string;
const chatOutline = require('../assets/icons/chat-outline.png') as string;
const chat = require('../assets/icons/chat.png') as string;
const circleDown = require('../assets/icons/circle-down.png') as string;
const circleLeft = require('../assets/icons/circle-left.png') as string;
const circleRight = require('../assets/icons/circle-right.png') as string;
const circleUp = require('../assets/icons/circle-up.png') as string;
const discountOutline =
  require('../assets/icons/discount-outline.png') as string;
const discount = require('../assets/icons/discount.png') as string;
const document = require('../assets/icons/document.png') as string;
const documentOutline =
  require('../assets/icons/document-outline.png') as string;
const downloadFile = require('../assets/icons/download-file.png') as string;
const email = require('../assets/icons/email.png') as string;
const emailOutline = require('../assets/icons/email-outline.png') as string;
const exploreOutline = require('../assets/icons/explore-outline.png') as string;
const explore = require('../assets/icons/explore.png') as string;
const fileUploadOutline =
  require('../assets/icons/file-upload-outline.png') as string;
const fileUpload = require('../assets/icons/file-upload.png') as string;
const folderOutline = require('../assets/icons/folder-outline.png') as string;
const folder = require('../assets/icons/folder.png') as string;
const graph = require('../assets/icons/graph.png') as string;
const graphOutline = require('../assets/icons/graph-outline.png') as string;
const heartOutline = require('../assets/icons/heart-outline.png') as string;
const heart = require('../assets/icons/heart.png') as string;
const hide = require('../assets/icons/hide.png') as string;
const home = require('../assets/icons/home.png') as string;
const home2 = require('../assets/icons/home2.png') as string;
const home2Outline = require('../assets/icons/home2-outline.png') as string;
const image = require('../assets/icons/image.png') as string;
const infoCircle = require('../assets/icons/info-circle.png') as string;
const locationOutline =
  require('../assets/icons/location-outline.png') as string;
const location = require('../assets/icons/location.png') as string;
const lock = require('../assets/icons/lock.png') as string;
const lockedComputerOutline =
  require('../assets/icons/locked-computer-outline.png') as string;
const logout = require('../assets/icons/logout.png') as string;
const loupe = require('../assets/icons/loupe.png') as string;
const microphone = require('../assets/icons/microphone.png') as string;
const moreCircle = require('../assets/icons/more-circle.png') as string;
const moreHorizontal = require('../assets/icons/more-horizontal.png') as string;
const moreVertical = require('../assets/icons/more-vertical.png') as string;
const padlock = require('../assets/icons/padlock.png') as string;
const paperOutline = require('../assets/icons/paper-outline.png') as string;
const paper = require('../assets/icons/paper.png') as string;
const playOutline = require('../assets/icons/play-outline.png') as string;
const play = require('../assets/icons/play.png') as string;
const plus = require('../assets/icons/plus.png') as string;
const search = require('../assets/icons/search.png') as string;
const sendOutline = require('../assets/icons/send-outline.png') as string;
const send = require('../assets/icons/send.png') as string;
const settingOutline = require('../assets/icons/setting-outline.png') as string;
const setting2Outline =
  require('../assets/icons/setting2-outline.png') as string;
const settings = require('../assets/icons/settings.png') as string;
const shieldOutline = require('../assets/icons/shield-outline.png') as string;
const shield = require('../assets/icons/shield.png') as string;
const show = require('../assets/icons/show.png') as string;
const squareInfo = require('../assets/icons/square-info.png') as string;
const starOutline = require('../assets/icons/star-outline.png') as string;
const star = require('../assets/icons/star.png') as string;
const ticketOutline = require('../assets/icons/ticket-outline.png') as string;
const ticket = require('../assets/icons/ticket.png') as string;
const timeCircle = require('../assets/icons/time-circle.png') as string;
const trash = require('../assets/icons/trash.png') as string;
const unlock = require('../assets/icons/unlock.png') as string;
const upAndDownArrow =
  require('../assets/icons/up-and-down-arrow.png') as string;
const userOutline = require('../assets/icons/user-outline.png') as string;
const user = require('../assets/icons/user.png') as string;
const videoCameraOutline =
  require('../assets/icons/video-camera-outline.png') as string;
const videoCamera = require('../assets/icons/video-camera.png') as string;
const voiceCommandOutline =
  require('../assets/icons/voice-command-outline.png') as string;
const voiceOutline = require('../assets/icons/voice-outline.png') as string;
const voice = require('../assets/icons/voice.png') as string;
const volumeDown = require('../assets/icons/volume-down.png') as string;
const volumeUp = require('../assets/icons/volume-up.png') as string;
const wallet = require('../assets/icons/wallet.png') as string;
const walletOutline = require('../assets/icons/wallet-outline.png') as string;
const wallet2 = require('../assets/icons/wallet2.png') as string;
const wallet2Outline = require('../assets/icons/wallet2-outline.png') as string;
const appleLogo = require('../assets/icons/apple-logo.png') as string;
const google = require('../assets/icons/google.png') as string;
const facebook = require('../assets/icons/facebook.png') as string;
const back = require('../assets/icons/back.png') as string;
const back2 = require('../assets/icons/back2.png') as string;
const arrowLeft = require('../assets/icons/arrow-left.png') as string;
const microphone2 = require('../assets/icons/microphone2.png') as string;
const microphone3 = require('../assets/icons/microphone3.png') as string;
const profile = require('../assets/icons/profile.png') as string;
const content = require('../assets/icons/content.png') as string;
const download = require('../assets/icons/download.png') as string;
const download2 = require('../assets/icons/download2.png') as string;
const notification = require('../assets/icons/notification.png') as string;
const security = require('../assets/icons/security.png') as string;
const menu = require('../assets/icons/menu.png') as string;
const more = require('../assets/icons/more.png') as string;
const moreInformation =
  require('../assets/icons/more-information.png') as string;
const premium1 = require('../assets/icons/premium1.png') as string;
const premium2 = require('../assets/icons/premium2.png') as string;
const apple = require('../assets/icons/apple.png') as string;
const paypal = require('../assets/icons/paypal.png') as string;
const creditCard = require('../assets/icons/credit-card.png') as string;
const share = require('../assets/icons/share.png') as string;
const shareOutline = require('../assets/icons/share-outline.png') as string;
const internet = require('../assets/icons/internet.png') as string;
const internetOutline =
  require('../assets/icons/internet-outline.png') as string;
const downSquare = require('../assets/icons/down-square.png') as string;
const downSquare2 = require('../assets/icons/down-square2.png') as string;
const rss = require('../assets/icons/rss.png') as string;
const playButton = require('../assets/icons/play-button.png') as string;
const pause = require('../assets/icons/pause.png') as string;
const backwardPlay = require('../assets/icons/backward-play.png') as string;
const forwardPlay = require('../assets/icons/forward-play.png') as string;
const forwardPlay2 = require('../assets/icons/forward-play-2.png') as string;
const nextPlay = require('../assets/icons/next-play.png') as string;
const previousPlay = require('../assets/icons/previous-play.png') as string;
const speedometer = require('../assets/icons/speedometer.png') as string;
const speedometer2 = require('../assets/icons/speedometer2.png') as string;
const stopwatch = require('../assets/icons/stopwatch.png') as string;
const cast = require('../assets/icons/cast.png') as string;
const history = require('../assets/icons/history.png') as string;
const userDefault = require('../assets/icons/user-default.png') as string;
const userDefault2 = require('../assets/icons/user-default2.png') as string;
const userDefault3 = require('../assets/icons/user-default3.png') as string;
const bell2 = require('../assets/icons/bell2.png') as string;
const bell3 = require('../assets/icons/bell3.png') as string;
const email2 = require('../assets/icons/email2.png') as string;
const cartOutline = require('../assets/icons/cart-outline.png') as string;
const cart = require('../assets/icons/cart.png') as string;
const chatBubble = require('../assets/icons/chat-bubble.png') as string;
const chatBubble2Outline =
  require('../assets/icons/chat-bubble2-outline.png') as string;
const chatBubble2 = require('../assets/icons/chat-bubble2.png') as string;
const document2Outline =
  require('../assets/icons/document2-outline.png') as string;
const document2 = require('../assets/icons/document2.png') as string;
const people = require('../assets/icons/people.png') as string;
const people2 = require('../assets/icons/people2.png') as string;
const people3 = require('../assets/icons/people3.png') as string;
const people4 = require('../assets/icons/people4.png') as string;
const mastercard = require('../assets/icons/mastercard.png') as string;
const facebook2 = require('../assets/icons/facebook2.png') as string;
const headset = require('../assets/icons/headset.png') as string;
const instagram = require('../assets/icons/instagram.png') as string;
const twitter = require('../assets/icons/twitter.png') as string;
const whatsapp = require('../assets/icons/whatsapp.png') as string;
const whatsapp2 = require('../assets/icons/whatsapp2.png') as string;
const world = require('../assets/icons/world.png') as string;
const world2 = require('../assets/icons/world2.png') as string;
const search2 = require('../assets/icons/search2.png') as string;
const search3 = require('../assets/icons/search3.png') as string;
const arrowDownSquare =
  require('../assets/icons/arrow-down-square.png') as string;
const arrowUpSquare = require('../assets/icons/arrow-up-square.png') as string;
const cancelSquare = require('../assets/icons/cancel-square.png') as string;
const cancelSquare2 = require('../assets/icons/cancel-square-2.png') as string;
const telephone = require('../assets/icons/telephone.png') as string;
const telephoneOutline =
  require('../assets/icons/telephone-outline.png') as string;
const mediumVolume = require('../assets/icons/medium-volume.png') as string;
const noSound = require('../assets/icons/no-sound.png') as string;
const videoCamera2 = require('../assets/icons/video-camera-2.png') as string;
const videoCamera2Off =
  require('../assets/icons/video-camera-2-off.png') as string;
const editPencil = require('../assets/icons/edit_pencil.png') as string;
const signature = require('../assets/icons/signature.png') as string;
const bookmark2 = require('../assets/icons/bookmark2.png') as string;
const bookmark2Outline =
  require('../assets/icons/bookmark2-outline.png') as string;
const notificationBell =
  require('../assets/icons/notification-bell.png') as string;
const notificationBell2 =
  require('../assets/icons/notification-bell2.png') as string;
const filter = require('../assets/icons/filter.png') as string;
const filter2 = require('../assets/icons/filter2.png') as string;
const filter3 = require('../assets/icons/filter3.png') as string;
const filter4 = require('../assets/icons/filter4.png') as string;
const box = require('../assets/icons/box.png') as string;
const rating = require('../assets/icons/rating.png') as string;
const star2 = require('../assets/icons/star2.png') as string;
const star3 = require('../assets/icons/star3.png') as string;
const star4 = require('../assets/icons/star4.png') as string;
const users = require('../assets/icons/users.png') as string;
const users2 = require('../assets/icons/users2.png') as string;
const time = require('../assets/icons/time.png') as string;
const clockTime = require('../assets/icons/clock-time.png') as string;
const figma = require('../assets/icons/figma.png') as string;
const heart2 = require('../assets/icons/heart2.png') as string;
const heart2Outline = require('../assets/icons/heart2_outline.png') as string;
const heart3 = require('../assets/icons/heart3.png') as string;
const heart4 = require('../assets/icons/heart4.png') as string;
const check = require('../assets/icons/check.png') as string;
const dashboardOutline =
  require('../assets/icons/dashboard-outline.png') as string;
const dashboard = require('../assets/icons/dashboard.png') as string;
const dashboard2 = require('../assets/icons/dashboard2.png') as string;
const dashboard2Outline =
  require('../assets/icons/dashboard2Outline.png') as string;
const send2 = require('../assets/icons/send2.png') as string;
const userLocation = require('../assets/icons/user-location.png') as string;
const bathtub = require('../assets/icons/bathtub.png') as string;
const bathtub2 = require('../assets/icons/bathtub2.png') as string;
const bed = require('../assets/icons/bed.png') as string;
const maximize = require('../assets/icons/maximize.png') as string;
const maximizeOutline =
  require('../assets/icons/maximize-outline.png') as string;
const car = require('../assets/icons/car.png') as string;
const car2 = require('../assets/icons/car2.png') as string;
const dumbell = require('../assets/icons/dumbell.png') as string;
const dumbell2 = require('../assets/icons/dumbell2.png') as string;
const fork = require('../assets/icons/fork.png') as string;
const laundry = require('../assets/icons/laundry.png') as string;
const pet = require('../assets/icons/pet.png') as string;
const pet2 = require('../assets/icons/pet2.png') as string;
const restaurant = require('../assets/icons/restaurant.png') as string;
const sport = require('../assets/icons/sport.png') as string;
const sport2 = require('../assets/icons/sport2.png') as string;
const swimming = require('../assets/icons/swimming.png') as string;
const swimming2 = require('../assets/icons/swimming2.png') as string;
const wifi = require('../assets/icons/wifi.png') as string;
const wifi2 = require('../assets/icons/wifi2.png') as string;
const pin = require('../assets/icons/pin.png');
const pinOutline = require('../assets/icons/pin-outline.png') as string;
const calendar4 = require('../assets/icons/calendar4.png') as string;
const location2 = require('../assets/icons/location2.png') as string;
const location2Outline =
  require('../assets/icons/location2-outline.png') as string;
const location3 = require('../assets/icons/location3.png') as string;
const location4 = require('../assets/icons/location4.png') as string;
const location5 = require('../assets/icons/location5.png') as string;
const rightArrow = require('../assets/icons/right-arrow.png') as string;
const location7 = require('../assets/icons/location7.png') as string;
const addUser = require('../assets/icons/add-user.png') as string;
const addUserOutline =
  require('../assets/icons/add-user-outline.png') as string;
const brain = require('../assets/icons/brain.png') as string;
const brain2 = require('../assets/icons/brain2.png') as string;
const children = require('../assets/icons/children.png') as string;
const children2 = require('../assets/icons/children2.png') as string;
const eye = require('../assets/icons/eye.png') as string;
const eye2 = require('../assets/icons/eye2.png') as string;
const friends = require('../assets/icons/friends.png') as string;
const joint = require('../assets/icons/joint.png') as string;
const joint2 = require('../assets/icons/joint2.png') as string;
const more2 = require('../assets/icons/more2.png') as string;
const nutrition1 = require('../assets/icons/nutrition.png') as string;
const nutrition2 = require('../assets/icons/nutrition2.png') as string;
const tooth = require('../assets/icons/tooth.png') as string;
const tooth2 = require('../assets/icons/tooth2.png') as string;
const more3 = require('../assets/icons/more3.png') as string;
const appointment = require('../assets/icons/appointment.png') as string;
const payment = require('../assets/icons/payment.png') as string;
const services = require('../assets/icons/services.png') as string;
const update = require('../assets/icons/update.png') as string;
const setup = require('../assets/icons/setup.png') as string;
const activity = require('../assets/icons/activity.png') as string;
const activity2 = require('../assets/icons/activity2.png') as string;
const starHalf = require('../assets/icons/star-half.png') as string;
const users3 = require('../assets/icons/users3.png') as string;
const calendar5 = require('../assets/icons/calendar5.png') as string;
const next = require('../assets/icons/next.png') as string;
const next2 = require('../assets/icons/next2.png') as string;
const down = require('../assets/icons/down.png') as string;
const memberCard = require('../assets/icons/member-card.png') as string;
const memberCardOutline =
  require('../assets/icons/member-card-outline.png') as string;
const idCard = require('../assets/icons/id-card.png') as string;
const idCardOutline = require('../assets/icons/id-card-outline.png') as string;
const license = require('../assets/icons/license.png') as string;
const certificate = require('../assets/icons/certificate.png') as string;
const identity = require('../assets/icons/identity.png') as string;
const image2 = require('../assets/icons/image2.png') as string;
const camera = require('../assets/icons/camera.png') as string;
const folder2 = require('../assets/icons/folder2.png') as string;
const facialRecognition =
  require('../assets/icons/facial-recognition.png') as string;
const facialRecognition2 =
  require('../assets/icons/facial-recognition2.png') as string;
const faceScan = require('../assets/icons/face-scan.png') as string;
const home3 = require('../assets/icons/home3.png') as string;
const home3Outline = require('../assets/icons/home3-outline.png') as string;
const home4 = require('../assets/icons/home4.png') as string;
const home4Outline = require('../assets/icons/home4-outline.png') as string;
const activityOutline =
  require('../assets/icons/activity-outline.png') as string;
const activity2Outline =
  require('../assets/icons/activity2-outline.png') as string;
const analytics = require('../assets/icons/analytics.png') as string;
const analyticsOutline =
  require('../assets/icons/analytics-outline.png') as string;
const analytics2 = require('../assets/icons/analytics2.png') as string;
const analytics2Outline =
  require('../assets/icons/analytics2-outline.png') as string;
const moreCircle2 = require('../assets/icons/more-circle2.png') as string;
const moreCircleOutline =
  require('../assets/icons/more-circle-outline.png') as string;
const scan2 = require('../assets/icons/scan2.png') as string;
const scan2Outline = require('../assets/icons/scan2-outline.png') as string;
const upload = require('../assets/icons/upload.png') as string;
const download3 = require('../assets/icons/download3.png') as string;
const edit = require('../assets/icons/edit.png') as string;
const pencil = require('../assets/icons/pencil.png') as string;
const checkBox = require('../assets/icons/check-box.png') as string;
const checkBoxOutline =
  require('../assets/icons/check-box-outline.png') as string;
const infoSquare = require('../assets/icons/info-square.png') as string;
const infoSquare2 = require('../assets/icons/info-square2.png') as string;
const squareCheckbox = require('../assets/icons/square-checkbox.png') as string;
const squareCheckbox2 =
  require('../assets/icons/square-checkbox2.png') as string;
const squareCheckbox3 =
  require('../assets/icons/square-checkbox3.png') as string;
const ticket2 = require('../assets/icons/ticket2.png') as string;
const ticket3 = require('../assets/icons/ticket3.png') as string;
const ticket4 = require('../assets/icons/ticket4.png') as string;
const wallet3 = require('../assets/icons/wallet3.png') as string;
const wallet4 = require('../assets/icons/wallet4.png') as string;
const profile2 = require('../assets/icons/profile2.png') as string;
const profile2Outline =
  require('../assets/icons/profile2-outline.png') as string;
const check2 = require('../assets/icons/check2.png') as string;
const check3 = require('../assets/icons/check3.png') as string;
const check4 = require('../assets/icons/check4.png') as string;
const bank = require('../assets/icons/bank.png') as string;
const masterCard = require('../assets/icons/mastercard.png') as string;
const visa = require('../assets/icons/visa.png') as string;
const electricity = require('../assets/icons/electricity.png') as string;
const games = require('../assets/icons/games.png') as string;
const health = require('../assets/icons/health.png') as string;
const installment = require('../assets/icons/installment.png') as string;
const internet2 = require('../assets/icons/internet2.png') as string;
const merchant = require('../assets/icons/merchant.png') as string;
const mobile = require('../assets/icons/mobile.png') as string;
const motor = require('../assets/icons/motor.png') as string;
const shopping = require('../assets/icons/shopping.png') as string;
const shopping2 = require('../assets/icons/shopping2.png') as string;
const television = require('../assets/icons/television.png') as string;
const television2 = require('../assets/icons/television2.png') as string;
const water = require('../assets/icons/water.png') as string;
const close = require('../assets/icons/close.png') as string;
const copy = require('../assets/icons/copy.png') as string;
const copy2 = require('../assets/icons/copy2.png') as string;
const send3 = require('../assets/icons/send3.png') as string;
const plus2 = require('../assets/icons/plus2.png') as string;
const plus3 = require('../assets/icons/plus3.png') as string;
const plus4 = require('../assets/icons/plus4.png') as string;
const close2 = require('../assets/icons/close2.png') as string;
const close3 = require('../assets/icons/close3.png') as string;
const edit2 = require('../assets/icons/edit2.png') as string;
const edit3 = require('../assets/icons/edit3.png') as string;
const people5 = require('../assets/icons/people5.png') as string;
const people6 = require('../assets/icons/people6.png') as string;
const people7 = require('../assets/icons/people7.png') as string;
const email3 = require('../assets/icons/email3.png') as string;
const work2 = require('../assets/icons/work2.png') as string;
const work3 = require('../assets/icons/work3.png') as string;
const tickSquare = require('../assets/icons/tick-square.png') as string;
const tickSquare2 = require('../assets/icons/tick-square2.png') as string;
const tickSquare3 = require('../assets/icons/tick-square3.png') as string;
const file = require('../assets/icons/file.png') as string;
const file2 = require('../assets/icons/file2.png') as string;
const notification2 = require('../assets/icons/notification2.png') as string;
const lock2 = require('../assets/icons/lock2.png') as string;
const shieldDone = require('../assets/icons/shield-done.png') as string;
const shieldDone2 = require('../assets/icons/shield-done2.png') as string;
const dashboard3 = require('../assets/icons/dashboard3.png') as string;
const view = require('../assets/icons/view.png') as string;
const eyeCloseUp = require('../assets/icons/eye-close-up.png') as string;
const closeSquare = require('../assets/icons/close-square.png') as string;
const logout2 = require('../assets/icons/logout2.png') as string;
const logout3 = require('../assets/icons/logout3.png') as string;
const mapLocation = require('../assets/icons/map_location.png');
const masterCardLogo = require('../assets/icons/mastercard_logo.png');
const dollarSymbol = require('../assets/icons/dollar-symbol.png');
const editService = require('../assets/icons/edit-service.png');
const editService2 = require('../assets/icons/edit-service2.png');
const infoService2 = require('../assets/icons/info-service2.png');
const info = require('../assets/icons/info.png');
const package1 = require('../assets/icons/package.png');
const package2 = require('../assets/icons/package2.png');
const cargo = require('../assets/icons/cargo.png');
const exchange = require('../assets/icons/exchange.png');
const boxSearch = require('../assets/icons/box-search.png');
const gps = require('../assets/icons/gps.png');
const routing = require('../assets/icons/routing.png');
const map = require('../assets/icons/map.png');
const box2 = require('../assets/icons/box2.png');
const balance = require('../assets/icons/balance.png');
const mastercardIcon = require('../assets/icons/mastercard_icon.png');
const balance2 = require('../assets/icons/balance2.png');
const mastercard2 = require('../assets/icons/mastercard2.png');

const envelop = require('../assets/icons/envelop.png');
const box_parcel = require('../assets/icons/box_parcel.png');
const backpack = require('../assets/icons/backpack.png');
const luggage = require('../assets/icons/luggage.png');
const other = require('../assets/icons/other.png');

const icons = {
  other,
  envelop,
  box_parcel,
  backpack,
  luggage,
  addFileOutline,
  addFile,
  arrowBack,
  arrowDown,
  arrowRight,
  arrowUp,
  bag,
  bellOutline,
  bell,
  bookmarkOutline,
  bookmark,
  calendar,
  calendar2,
  calendar3,
  call,
  categoryOutline,
  category,
  chatOutline,
  chat,
  circleDown,
  circleLeft,
  circleRight,
  circleUp,
  discountOutline,
  discount,
  document,
  documentOutline,
  downloadFile,
  email,
  emailOutline,
  exploreOutline,
  explore,
  fileUploadOutline,
  fileUpload,
  folderOutline,
  folder,
  graph,
  graphOutline,
  heartOutline,
  heart,
  hide,
  home,
  home2,
  home2Outline,
  image,
  infoCircle,
  locationOutline,
  location,
  lock,
  lockedComputerOutline,
  logout,
  loupe,
  microphone,
  moreCircle,
  moreHorizontal,
  moreVertical,
  padlock,
  paperOutline,
  paper,
  playOutline,
  play,
  plus,
  search,
  sendOutline,
  send,
  settingOutline,
  setting2Outline,
  settings,
  shieldOutline,
  shield,
  show,
  squareInfo,
  starOutline,
  star,
  ticketOutline,
  ticket,
  timeCircle,
  trash,
  unlock,
  upAndDownArrow,
  userOutline,
  user,
  videoCameraOutline,
  videoCamera,
  voiceCommandOutline,
  voiceOutline,
  voice,
  volumeDown,
  volumeUp,
  wallet,
  walletOutline,
  wallet2,
  wallet2Outline,
  appleLogo,
  google,
  facebook,
  back,
  back2,
  arrowLeft,
  microphone2,
  microphone3,
  profile,
  content,
  download,
  download2,
  notification,
  security,
  more,
  moreInformation,
  menu,
  premium1,
  premium2,
  apple,
  paypal,
  creditCard,
  share,
  shareOutline,
  internet,
  internetOutline,
  downSquare,
  downSquare2,
  rss,
  playButton,
  pause,
  backwardPlay,
  forwardPlay,
  forwardPlay2,
  nextPlay,
  previousPlay,
  speedometer,
  speedometer2,
  stopwatch,
  cast,
  history,
  userDefault,
  userDefault2,
  userDefault3,
  bell2,
  bell3,
  email2,
  cartOutline,
  chatBubble,
  chatBubble2Outline,
  chatBubble2,
  document2Outline,
  document2,
  people,
  people2,
  people3,
  people4,
  mastercard,
  facebook2,
  headset,
  instagram,
  twitter,
  whatsapp,
  whatsapp2,
  world,
  world2,
  cart,
  search2,
  search3,
  arrowDownSquare,
  arrowUpSquare,
  cancelSquare,
  cancelSquare2,
  telephone,
  telephoneOutline,
  mediumVolume,
  noSound,
  videoCamera2,
  videoCamera2Off,
  editPencil,
  signature,
  bookmark2,
  bookmark2Outline,
  notificationBell,
  notificationBell2,
  filter,
  filter2,
  filter3,
  filter4,
  box,
  rating,
  star2,
  star3,
  star4,
  users,
  users2,
  time,
  clockTime,
  figma,
  heart2,
  heart2Outline,
  heart3,
  heart4,
  check,
  dashboardOutline,
  dashboard,
  dashboard2,
  dashboard2Outline,
  send2,
  userLocation,
  bathtub,
  bathtub2,
  bed,
  maximize,
  maximizeOutline,
  car,
  car2,
  dumbell,
  dumbell2,
  fork,
  laundry,
  pet,
  pet2,
  restaurant,
  sport,
  sport2,
  swimming,
  swimming2,
  wifi,
  wifi2,
  pin,
  pinOutline,
  calendar4,
  location2,
  location2Outline,
  location3,
  location4,
  location5,
  rightArrow,
  location7,
  addUser,
  addUserOutline,
  brain,
  brain2,
  children,
  children2,
  eye,
  eye2,
  friends,
  joint,
  joint2,
  more2,
  nutrition1,
  nutrition2,
  tooth,
  tooth2,
  more3,
  appointment,
  payment,
  services,
  update,
  setup,
  activity,
  activity2,
  starHalf,
  users3,
  calendar5,
  next,
  next2,
  down,
  memberCard,
  memberCardOutline,
  idCard,
  idCardOutline,
  license,
  certificate,
  identity,
  image2,
  camera,
  folder2,
  faceScan,
  facialRecognition,
  facialRecognition2,
  home3,
  home3Outline,
  home4,
  home4Outline,
  activityOutline,
  activity2Outline,
  analytics,
  analyticsOutline,
  analytics2,
  analytics2Outline,
  moreCircle2,
  moreCircleOutline,
  scan2,
  scan2Outline,
  upload,
  download3,
  edit,
  pencil,
  checkBox,
  checkBoxOutline,
  infoSquare,
  infoSquare2,
  squareCheckbox,
  squareCheckbox2,
  squareCheckbox3,
  ticket2,
  ticket3,
  ticket4,
  wallet3,
  wallet4,
  profile2,
  profile2Outline,
  check2,
  check3,
  check4,
  masterCard,
  visa,
  bank,

  electricity,
  games,
  health,
  installment,
  internet2,
  merchant,
  mobile,
  motor,
  shopping,
  shopping2,
  television,
  television2,
  water,
  close,
  copy,
  copy2,
  send3,
  plus2,
  plus3,
  plus4,
  close2,
  close3,
  edit2,
  edit3,
  people5,
  people6,
  people7,
  email3,
  work2,
  work3,
  tickSquare,
  tickSquare2,
  tickSquare3,
  file,
  file2,
  notification2,
  lock2,
  shieldDone,
  shieldDone2,
  dashboard3,
  view,
  eyeCloseUp,
  closeSquare,
  logout2,
  logout3,
  mapLocation,
  masterCardLogo,
  dollarSymbol,
  editService,
  editService2,
  infoService2,
  info,
  package1,
  package2,
  cargo,
  exchange,
  boxSearch,
  gps,
  routing,
  map,
  box2,
  balance,
  mastercardIcon,
  balance2,
  mastercard2,
};

export default icons;
