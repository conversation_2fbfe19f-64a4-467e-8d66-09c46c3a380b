const logo = require("../assets/images/logo.png") as string;
const onboardingSplash = require("../assets/images/onboarding_splash1.jpg");
const onboardingSplashDark = require("../assets/images/onboarding_splash1_dark.jpg");
const idAvatar = require("../assets/images/id_avatar.png");
const avatar = require("../assets/images/avatar.png");
const card = require("../assets/images/card.png");
const face = require("../assets/images/face.png");
const scanCard = require("../assets/images/scan_card.png");
const user1 = require("../assets/images/users/user1.jpeg");
const user2 = require("../assets/images/users/user2.jpeg");
const user3 = require("../assets/images/users/user3.jpeg");
const user4 = require("../assets/images/users/user4.jpeg");
const user5 = require("../assets/images/users/user5.jpeg");
const user6 = require("../assets/images/users/user6.jpeg");
const user7 = require("../assets/images/users/user7.jpeg");
const user8 = require("../assets/images/users/user8.jpeg");
const user9 = require("../assets/images/users/user9.jpeg");
const user10 = require("../assets/images/users/user10.jpeg");
const user11 = require("../assets/images/users/user11.jpeg");
const elipseCard = require("../assets/images/elipse-card.png");
const rectangleCard = require("../assets/images/rectangle-card.png");
const orderSuccess = require("../assets/images/order_success.png");
const package1 = require("../assets/images/packages/package1.jpeg");
const package2 = require("../assets/images/packages/package2.jpeg");
const package3 = require("../assets/images/packages/package3.jpeg");
const package4 = require("../assets/images/packages/package4.jpeg");
const package5 = require("../assets/images/packages/package5.jpeg");
const package6 = require("../assets/images/packages/package6.jpeg");
const package7 = require("../assets/images/packages/package7.jpeg");

const images = {
    logo,
    onboardingSplash,
    onboardingSplashDark,
    idAvatar,
    avatar,
    card,
    face,
    scanCard,
    user1,
    user2,
    user3,
    user4,
    user5,
    user6,
    user7,
    user8,
    user9,
    user10,
    user11,
    elipseCard,
    rectangleCard,
    orderSuccess,
    package1,
    package2,
    package3,
    package4,
    package5,
    package6,
    package7
};

export default images;