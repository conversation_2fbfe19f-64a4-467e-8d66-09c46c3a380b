const onboarding1 = require("../assets/illustrations/onboarding1.png");
const onboarding2 = require("../assets/illustrations/onboarding2.png");
const onboarding3 = require("../assets/illustrations/onboarding3.png");
const onboarding1Dark = require("../assets/illustrations/onboarding1_dark.png"); 
const onboarding2Dark = require("../assets/illustrations/onboarding2_dark.png");
const onboarding3Dark = require("../assets/illustrations/onboarding3_dark.png");
const password = require("../assets/illustrations/password.png");
const passwordDark = require("../assets/illustrations/password_dark.png");
const passwordSuccess = require("../assets/illustrations/password_success.png");
const newPassword = require("../assets/illustrations/new_password.png");
const passwordSuccessDark = require("../assets/illustrations/password_success_dark.png");
const fingerprint = require("../assets/illustrations/fingerprint.png");
const identity = require("../assets/illustrations/identity.png");
const identityDark = require("../assets/illustrations/identity_dark.png");
const card = require("../assets/illustrations/card.png");
const services = require("../assets/illustrations/services.png");
const servicesDark = require("../assets/illustrations/services_dark.png");
const friends = require("../assets/illustrations/friends.png");
const friendsDark = require("../assets/illustrations/friends_dark.png");
const successPaymentType = require("../assets/illustrations/success_payment_type.png");
const successPaymentTypeDark = require("../assets/illustrations/success_payment_type_dark.png");
const requestMoney = require("../assets/illustrations/request_money.png");
const requestMoneyDark = require("../assets/illustrations/request_money_dark.png");
const bankSuccess = require("../assets/illustrations/bank_success.png");
const bankSuccessDark = require("../assets/illustrations/bank_success_dark.png");
const quickInvoice = require("../assets/illustrations/quick_invoice.png");
const quickInvoiceDark = require("../assets/illustrations/quick_invoice_dark.png");
const payBills = require("../assets/illustrations/pay_bills.png");
const payBillsDark = require("../assets/illustrations/pay_bills_dark.png");
const splitBill = require("../assets/illustrations/split_bill.png");
const splitBillDark = require("../assets/illustrations/split_bill_dark.png");
const refundSuccess = require("../assets/illustrations/refund_success.png");
const refundSuccessDark = require("../assets/illustrations/refund_success_dark.png");
const blockedContacts = require("../assets/illustrations/blocked_contacts.png");
const blockedContactsDark = require("../assets/illustrations/blocked_contacts_dark.png");
const correctData = require("../assets/illustrations/correct_data.png");
const correctDataDark = require("../assets/illustrations/correct_data_dark.png");
const downloadData = require("../assets/illustrations/download_data.png");
const downloadDataDark = require("../assets/illustrations/download_data_dark.png");
const permissionGiven = require("../assets/illustrations/permission_given.png");
const permissionGivenDark = require("../assets/illustrations/permission_given_dark.png");
const searchPrivacy = require("../assets/illustrations/search_privacy.png");
const searchPrivacyDark = require("../assets/illustrations/search_privacy_dark.png");
const background = require("../assets/illustrations/background.png");
const notFound = require("../assets/illustrations/not_found.png");

const illustrations = {
    onboarding1,
    onboarding2,
    onboarding3,
    password,
    passwordDark,
    passwordSuccess,
    newPassword,
    passwordSuccessDark,
    fingerprint,
    identity,
    identityDark,
    card,
    services,
    friends,
    successPaymentType,
    requestMoney,
    bankSuccess,
    quickInvoice,
    quickInvoiceDark,
    payBills,
    splitBill,
    refundSuccess,
    blockedContacts,
    correctData,
    downloadData,
    permissionGiven,
    searchPrivacy,
    onboarding1Dark,
    onboarding2Dark,
    onboarding3Dark,
    successPaymentTypeDark,
    requestMoneyDark,
    bankSuccessDark,
    payBillsDark,
    splitBillDark,
    refundSuccessDark,
    servicesDark,
    friendsDark,
    blockedContactsDark,
    correctDataDark,
    downloadDataDark,
    permissionGivenDark,
    searchPrivacyDark,
    background,
    notFound
}

export default illustrations;