// rootReducer.ts
import { combineReducers } from "@reduxjs/toolkit";
import authReducer from "./auth/authSlice";
import parcelsReducer from "./parcels/parcelsSlice";
import walletReducer from "./wallet/walletSlice";

const rootReducer = combineReducers({
  auth: authReducer,
  parcels: parcelsReducer,
  wallet: walletReducer,
});

export type RootState = ReturnType<typeof rootReducer>;

export default rootReducer;
