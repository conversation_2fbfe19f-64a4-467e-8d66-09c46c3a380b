import axios from 'axios';

const baseUrl = 'http://10.0.2.2:8000/';

const getCategory = async (token: string) => {
  try {
    const response = await axios.get(
      `${baseUrl}api/user/categories`, // Replace with your API URL
      {
        headers: {
          'x-access-token': token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('getCategory failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'getCategory failed');
  }
};

const createParcel = async (body: any) => {
  try {
    const response = await axios.post(
      `${baseUrl}api/parcels`, // Replace with your API URL
      body.data,
      {
        headers: {
          'x-access-token': body.token,
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('createParcel failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'createParcel failed');
  }
};

const updateParcel = async (body: any) => {
  try {
    const response = await axios.put(
      `${baseUrl}api/parcels/${body.id}`, // Replace with your API URL
      body.data,
      {
        headers: {
          'x-access-token': body.token,
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('getCategory failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'getCategory failed');
  }
};

const getMarketPlace = async (token: string) => {
  try {
    const response = await axios.get(
      `${baseUrl}api/marketplace/search`, // Replace with your API URL
      {
        headers: {
          'x-access-token': token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('getParcel failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'getParcel failed');
  }
};

const getParcels = async (token: string) => {
  try {
    const response = await axios.get(
      `${baseUrl}api/parcels`, // Replace with your API URL
      {
        headers: {
          'x-access-token': token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('getParcel failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'getParcel failed');
  }
};

const getAcceptedParcels = async (token: string) => {
  try {
    const response = await axios.get(
      `${baseUrl}api/accepted/parcels`, // Replace with your API URL
      {
        headers: {
          'x-access-token': token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('getAcceptedParcels failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'getParcel failed');
  }
};

const getParcelById = async (data: {token: string; id: string}) => {
  try {
    const response = await axios.get(
      `${baseUrl}api/parcels/${data.id}`, // Replace with your API URL
      {
        headers: {
          'x-access-token': data.token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('getParcel failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'getParcel failed');
  }
};

const acceptParcel = async (data: {token: string; id: string}) => {
  try {
    const response = await axios.post(
      `${baseUrl}api/parcels/accept`, // Replace with your API URL
      {
        parcel_id: data.id,
      },
      {
        headers: {
          'x-access-token': data.token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('getParcel failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'getParcel failed');
  }
};

const pickedParcel = async (data: {token: string; id: string, otp: string}) => {
  try {
    const response = await axios.post(
      `${baseUrl}api/parcel/pickup`, // Replace with your API URL
      {
        parcel_id: data.id,
        otp: data.otp
      },
      {
        headers: {
          'x-access-token': data.token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('pickedParcel failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'pickedParcel failed');
  }
};


const deliverParcel = async (data: {token: string; id: string, otp: string}) => {
  try {
    console.log('deliverParcel response:', data);
    
    const response = await axios.post(
      `${baseUrl}api/parcel/deliver`, // Replace with your API URL
      {
        parcel_id: data.id,
        otp: data.otp
      },
      {
        headers: {
          'x-access-token': data.token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('deliverParcel failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'deliverParcel failed');
  }
};

const getRecentParcel = async (token: string) => {
  try {
    const response = await axios.get(
      `${baseUrl}api/recentParcel`,
      {
        headers: {
          'x-access-token': token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('getRecentParcel failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'getRecentParcel failed');
  }
};

const cancelParcel = async (data: {token: string; id: string}) => {
  try {
    const response = await axios.post(
      `${baseUrl}api/parcels/cancel-acceptance`, // Replace with your API URL
      {
        parcel_id: data.id,
      },
      {
        headers: {
          'x-access-token': data.token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('cancelParcel failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'cancelParcel failed');
  }
};

export default {
  getCategory,
  createParcel,
  getParcels,
  getMarketPlace,
  updateParcel,
  getParcelById,
  acceptParcel,
  pickedParcel,
  deliverParcel,
  getRecentParcel,
  getAcceptedParcels,
  cancelParcel
};
