import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import parcelsService from './parcelsService';

export interface Category {
  id: number;
  name: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  subcategories: any[];
}

export interface Acceptance {
  id: number
  parcel_id: number
  delivery_user_id: number
  status: string
  otp: string
  accepted_at: string
  picked_at: any
  delivered_at: any
  createdAt: string
  updatedAt: string
  deliveryUser: DeliveryUser
}

export interface DeliveryUser {
  id: number
  name: string
  email: string
  password: string
  otp: string
  verification_token: any
  status: string
  createdAt: string
  updatedAt: string
}

export interface User {
  id: number
  name: string
  email: string
  password: string
  otp: string
  verification_token: any
  status: string
  createdAt: string
  updatedAt: string
  profile: Profile
}

export interface Profile {
  id: number
  profile_pic_hash: string
  profile_pic_ext: string
  gender: string
  phone_number: string
  date_of_birth: string
  userId: number
  createdAt: string
  updatedAt: string
}


export interface Parcel {
  id: number
  userId: number
  categoryId: number
  full_name: string
  mobile: string
  amount: string
  weight: string
  insured: boolean
  pickup_lat: string
  pickup_long: string
  pickup_address: string
  delivery_lat: string
  delivery_long: string
  delivery_address: string
  description: string
  package_type: string
  status: string
  createdAt: string
  updatedAt: string
  media: Medum[]
  category: Category
  title: string
  acceptance: Acceptance
  user: User
  email: string
}

export interface Medum {
  id: number
  parcelId: number
  mediaUrl: string
}

export interface Category {
  id: number
  name: string
  status: string
  createdAt: string
  updatedAt: string
}

interface ParcelState {
  categoryList: Category[];
  myParcels: Parcel[];
  marketPlaceList: Parcel[];
  acceptedParcels: Parcel[];
  recentParcel: Parcel[];
}

const initialState: ParcelState = {
  categoryList: [],
  myParcels: [],
  marketPlaceList: [],
  acceptedParcels: [],
  recentParcel: []
};

export const getCategory: any = createAsyncThunk(
  'parcel/getCategory',
  async (token: string, thunkAPI) => {
    try {
      const response = await parcelsService.getCategory(token);
      
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const createParcel: any = createAsyncThunk(
  'parcel/createParcel',
  async (body: any, thunkAPI) => {
    try {
      const response = await parcelsService.createParcel(body);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const updateParcel: any = createAsyncThunk(
  'parcel/updateParcel',
  async (body: any, thunkAPI) => {
    try {
      const response = await parcelsService.updateParcel(body);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getParcels: any = createAsyncThunk(
  'parcel/getParcels',
  async (token: string, thunkAPI) => {
    try {
      const response = await parcelsService.getParcels(token);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const getAcceptedParcels: any = createAsyncThunk(
  'parcel/getAcceptedParcels',
  async (token: string, thunkAPI) => {
    try {
      const response = await parcelsService.getAcceptedParcels(token);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const getMarketPlace: any = createAsyncThunk(
  'parcel/getMarketPlace',
  async (token: string, thunkAPI) => {
    try {
      const response = await parcelsService.getMarketPlace(token);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getParcelById: any = createAsyncThunk(
  'parcel/getParcelById',
  async (data: {token: string; id: string}, thunkAPI) => {
    try {
      const response = await parcelsService.getParcelById(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const acceptParcel: any = createAsyncThunk(
  'parcel/acceptParcel',
  async (data: {token: string; id: string}, thunkAPI) => {
    try {
      const response = await parcelsService.acceptParcel(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const pickedParcel: any = createAsyncThunk(
  'parcel/pickedParcel',
  async (data: {token: string; id: string; otp: string}, thunkAPI) => {
    try {
      const response = await parcelsService.pickedParcel(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const deliverParcel: any = createAsyncThunk(
  'parcel/deliverParcel',
  async (data: {token: string; id: string; otp: string}, thunkAPI) => {
    try {
      const response = await parcelsService.deliverParcel(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const getRecentParcel: any = createAsyncThunk(
  'parcel/getRecentParcel',
  async (token: string, thunkAPI) => {
    try {
      const response = await parcelsService.getRecentParcel(token);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const cancelParcel: any = createAsyncThunk(
  'parcel/cancelParcel',
  async (data: {token: string; id: string}, thunkAPI) => {
    try {
      const response = await parcelsService.cancelParcel(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

const parcelsSlice = createSlice({
  name: 'parcels',
  initialState,
  reducers: {},
  extraReducers(builder) {
    builder.addCase(getCategory.fulfilled, (state, action) => {
      state.categoryList = action.payload.categories;
    });
    builder.addCase(getParcels.fulfilled, (state, action) => {
      state.myParcels = action.payload.parcels;
    });
    builder.addCase(getMarketPlace.fulfilled, (state, action) => {
      state.marketPlaceList = action.payload.parcels;
    });
    builder.addCase(getAcceptedParcels.fulfilled, (state, action) => {
      state.acceptedParcels = action.payload.parcels;
    })
    builder.addCase(getRecentParcel.fulfilled, (state, action) => {
      state.recentParcel = action.payload.parcels;
    });;
  },
});

export default parcelsSlice.reducer;
