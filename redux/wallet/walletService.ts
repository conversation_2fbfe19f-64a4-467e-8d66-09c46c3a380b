import axios from 'axios';

const baseUrl = 'http://10.0.2.2:8000/';

const getWallet = async (data: {token: string; userId: string}) => {
  try {
    const response = await axios.get(
      `${baseUrl}api/wallet/${data.userId}`, // Replace with your API URL
      {
        headers: {
          'x-access-token': data.token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('getCategory failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'getCategory failed');
  }
};

const addAmountToWallet = async (body: {
  token: string;
  userId: string;
  description: string;
  amount: string;
}) => {
  const payload = {
    amount: body.amount,
    description: body.description,
  };
  try {
    const response = await axios.post(
      `${baseUrl}api/wallet/${body.userId}/add`,
      payload,
      {
        headers: {
          'x-access-token': body.token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error(
      'createParcel failed:',
      error.response?.data || error.message,
    );
    throw new Error(error.response?.data?.message || 'createParcel failed');
  }
};

export default {
  getWallet,
  addAmountToWallet,
};
