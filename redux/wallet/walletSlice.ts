import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import walletService from './walletService';

export interface Wallet {
  id: number
  balance: string
  createdAt: string
  updatedAt: string
  userId: number
  transactions: Transaction[]
}

export interface Transaction {
  id: number
  amount: string
  type: string
  description: string
  createdAt: string
  updatedAt: string
  walletId: number
}


interface WalletState {
  wallet: Wallet | null;
}

const initialState: WalletState = {
  wallet: null
};


export const getWallet: any = createAsyncThunk(
  'wallet/getWallet',
  async (data: {token: string; userId: string;}, thunkAPI) => {
    try {
      const response = await walletService.getWallet(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const addAmountToWallet: any = createAsyncThunk(
  'wallet/addAmountToWallet',
  async (data: {token: string; userId: string; description: string, amount: string}, thunkAPI) => {
    try {
      const response = await walletService.addAmountToWallet(data);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

const parcelsSlice = createSlice({
  name: 'wallet',
  initialState,
  reducers: {},
  extraReducers(builder) {
    builder.addCase(getWallet.fulfilled, (state, action) => {
      state.wallet = action.payload;
    });
  },
});

export default parcelsSlice.reducer;
