import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import authService from './authService';
import {ChangePasswordRequest, OTPVerifyRequest, UserLoginRequest, UserRegisterRequest} from '../../types/auth';

export interface User {
  status: boolean;
  message: string;
  token?: string;
  userData?: UserData;
}

export interface UserData {
  id: number;
  name: string;
  email: string;
  otp: string;
  verification_token: any;
  status: string;
  createdAt: string;
  updatedAt: string;
  profile: Profile;
  imageUrl: string;
}

export interface Profile {
  id: number;
  profile_pic_hash: any;
  profile_pic_ext: any;
  gender: any;
  phone_number: any;
  date_of_birth: any;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

interface AuthState {
  userData: User | null;
}

const initialState: AuthState = {
  userData: null,
};

export const signUpAction: any = createAsyncThunk(
  'user/signUpAction',
  async (body: UserRegisterRequest, thunkAPI) => {
    try {
      const response = await authService.signUpUser(body);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const loginAction: any = createAsyncThunk(
  'user/loginAction',
  async (body: UserLoginRequest, thunkAPI) => {
    try {
      const response = await authService.loginUser(body);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const forgetPassword: any = createAsyncThunk(
  'user/forgetPassword',
  async (email: string, thunkAPI) => {
    try {
      const response = await authService.forgetPassword(email);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const verifyOTP: any = createAsyncThunk(
  'user/verifyOTP',
  async (body: OTPVerifyRequest, thunkAPI) => {
    try {
      const response = await authService.verifyOTP(body);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const saveUserData: any = createAsyncThunk(
  'user/saveUserData',
  async (body: any, thunkAPI) => {
    try {
      return body;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

export const changePassword: any = createAsyncThunk(
  'user/changePassword',
  async (body: ChangePasswordRequest, thunkAPI) => {
    try {
      const response = await authService.changePassword(body);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const getProfile: any = createAsyncThunk(
  'user/getProfile',
  async (token: string, thunkAPI) => {
    try {
      const response = await authService.getProfile(token);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);


export const updateProfile: any = createAsyncThunk(
  'user/updateProfile',
  async (body: any, thunkAPI) => {
    try {
      const response = await authService.updateProfile(body);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  },
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logoutUser: state => {
      state.userData = null;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(signUpAction.fulfilled, (state, action) => {
        console.log('signUpAction is fulfilled', action.payload);
        state.userData = action.payload;
      })
      .addCase(loginAction.fulfilled, (state, action) => {
        console.log('loginAction is fulfilled', action.payload);
        state.userData = action.payload;
      })
      .addCase(saveUserData.fulfilled, (state, action) => {
        state.userData = action.payload;
      })
      .addCase(getProfile.fulfilled, (state, action) => {
        state.userData = action.payload;
      });
  },
});

export const {logoutUser} = authSlice.actions;
export default authSlice.reducer;
