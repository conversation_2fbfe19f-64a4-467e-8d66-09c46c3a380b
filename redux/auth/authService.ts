import axios from 'axios';
import {
  ChangePasswordRequest,
  OTPVerifyRequest,
  UserLoginRequest,
  UserRegisterRequest,
} from '../../types/auth';

const baseUrl = 'http://10.0.2.2:8000/';

const signUpUser = async (body: UserRegisterRequest) => {
  try {
    const response = await axios.post(
      `${baseUrl}api/user/register`, // Replace with your API URL
      body,
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('Signup failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Signup failed');
  }
};

const loginUser = async (body: UserLoginRequest) => {
  try {
    const response = await axios.post(
      `${baseUrl}api/user/login`, // Replace with your API URL
      body,
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('Login failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Login failed');
  }
};

const forgetPassword = async (email: string) => {
  try {
    const response = await axios.post(
      `${baseUrl}api/user/forgotPassword`, // Replace with your API URL
      {email},
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error(
      'ForgotPassword failed:',
      error.response?.data || error.message,
    );
    throw new Error(error.response?.data?.message || 'ForgotPassword failed');
  }
};

const verifyOTP = async (body: OTPVerifyRequest) => {
  try {
    const response = await axios.post(
      `${baseUrl}api/user/verifyOtp`, // Replace with your API URL
      body,
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('Verify failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Verify failed');
  }
};

const changePassword = async (body: ChangePasswordRequest) => {
  try {
    const response = await axios.post(
      `${baseUrl}api/user/createNewPassword`, // Replace with your API URL
      body,
      {
        headers: {
          'x-access-token': body.token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error(
      'changePassword failed:',
      error.response?.data || error.message,
    );
    throw new Error(error.response?.data?.message || 'changePassword failed');
  }
};

const getProfile = async (token: string) => {
  try {
    const response = await axios.get(
      `${baseUrl}api/profile/getProfile`, // Replace with your API URL
      {
        headers: {
          'x-access-token': token,
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error('getProfile failed:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'getProfile failed');
  }
};

const updateProfile = async (body: any) => {
  try {
    const response = await axios.post(
      `${baseUrl}api/profile/updateProfile`, // Replace with your API URL
      body.data,
      {
        headers: {
          'x-access-token': body.token,
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    return response.data; // Return response data
  } catch (error: any) {
    console.error(
      'updateProfile failed:',
      error.response?.data || error.message,
    );
    throw new Error(error.response?.data?.message || 'updateProfile failed');
  }
};

export default {
  signUpUser,
  loginUser,
  forgetPassword,
  verifyOTP,
  changePassword,
  getProfile,
  updateProfile,
};
