import {images} from '../constants';

export const userAddresses = [
  {
    id: '1',
    name: 'Home',
    address: '364 Stillwater Ave, Attleboro, MA 02703',
  },
  {
    id: '2',
    name: 'Office',
    address: '73 Virginia Rd, Cuyahoga Falls, OH 44221',
  },
  {
    id: '3',
    name: 'Mall Plaza',
    address: '123 Main St, San Francisco, CA 94107',
  },
  {
    id: '4',
    name: 'Garden Park',
    address: '600 Bloom St, Portland, OR 97201',
  },
  {
    id: '5',
    name: 'Grand City Park',
    address: '26 State St Daphne, AL 36526',
  },
  {
    id: '6',
    name: 'Town Square',
    address: '20 Applegate St. Hoboken, NJ 07030',
  },
  {
    id: '7',
    name: 'Bank',
    address: '917 W Pine Street Easton, PA 0423',
  },
];

export const faqKeywords = [
  {
    id: '1',
    name: 'General',
  },
  {
    id: '2',
    name: 'Account',
  },
  {
    id: '3',
    name: 'Security',
  },
  {
    id: '4',
    name: 'Tracking',
  },
  {
    id: '5',
    name: 'Issues',
  },
  {
    id: '6',
    name: 'Services',
  },
];

export const faqs = [
  {
    question: 'How do I schedule a new delivery?',
    answer:
      'To schedule a new delivery, log in to the app, go to the "Deliveries" section, and follow the prompts to create a new delivery request. You will need to provide details about the pickup and drop-off locations, as well as the package information.',
    type: 'Services',
  },
  {
    question: 'How can I reset my account password?',
    answer:
      'To reset your account password, go to the login screen and click on "Forgot Password". Follow the instructions to reset your password. You may need to verify your identity using your registered email or phone number.',
    type: 'Security',
  },
  {
    question: 'What should I do if I receive a damaged package?',
    answer:
      'If you receive a damaged package, immediately contact customer support through the app. You can also report the issue in the "Orders" section to initiate a return or refund process.',
    type: 'Issues',
  },
  {
    question: 'How do I track my delivery?',
    answer:
      'To track your delivery, go to the "Track" section of the app, enter your tracking number, and you will be able to see the real-time status of your package.',
    type: 'Tracking',
  },
  {
    question: 'Can I update my delivery address?',
    answer:
      'Yes, you can update your delivery address before the delivery is dispatched. Go to the "Orders" section, select the delivery you want to update, and follow the instructions to change the address.',
    type: 'Account',
  },
  {
    question: 'How do I update my contact information?',
    answer:
      'To update your contact information, go to your account settings, and you will find options to edit your profile details such as your address, phone number, and email.',
    type: 'Account',
  },
  {
    question: 'How secure is my information in the app?',
    answer:
      'We prioritize the security of your information. Our app uses advanced encryption and security protocols to ensure that your personal data is protected.',
    type: 'Security',
  },
  {
    question: 'How can I view my delivery history?',
    answer:
      'To view your delivery history, go to the "Orders" section of the app. You can filter deliveries by date, status, and recipient.',
    type: 'Tracking',
  },
  {
    question: 'What types of delivery services are available?',
    answer:
      'We offer various types of delivery services including standard delivery, express delivery, and same-day delivery. Visit the "Services" section of the app for more details and to choose the service that best suits your needs.',
    type: 'Services',
  },
  {
    question: 'How can I contact customer support?',
    answer:
      'To contact customer support, go to the "Help" section in the app. You can reach us via chat, email, or phone. Our support team is available 24/7 to assist you.',
    type: 'General',
  },
];

export const friends = [
  {
    id: '1',
    name: 'Tynisa Obey',
    phoneNumber: '******-400-0135',
    avatar: images.user1,
  },
  {
    id: '2',
    name: 'Florencio Dorance',
    phoneNumber: '******-900-0135',
    avatar: images.user2,
  },
  {
    id: '3',
    name: 'Chantal Shelburne',
    phoneNumber: '******-100-1009',
    avatar: images.user3,
  },
  {
    id: '4',
    name: 'Maryland Winkles',
    phoneNumber: '******-200-4550',
    avatar: images.user4,
  },
  {
    id: '5',
    name: 'Rodolfo Goode',
    phoneNumber: '******-200-9800',
    avatar: images.user5,
  },
  {
    id: '6',
    name: 'Benny Spanbauer',
    phoneNumber: '******-200-9800',
    avatar: images.user6,
  },
  {
    id: '7',
    name: 'Tyra Dillon',
    phoneNumber: '******-230-9899',
    avatar: images.user7,
  },
  {
    id: '8',
    name: 'Jamel Eusobio',
    phoneNumber: '******-234-9899',
    avatar: images.user8,
  },
  {
    id: '9',
    name: 'Pedro Haurad',
    phoneNumber: '******-234-9899',
    avatar: images.user9,
  },
  {
    id: '10',
    name: 'Clinton Mcclure',
    phoneNumber: '******-234-4555',
    avatar: images.user10,
  },
];

export const notifications = [
  {
    id: '1',
    title: 'Security Updates!',
    description:
      'Our app now supports Two-Factor Authentication. Enable it now to make your account more secure.',
    date: '2024-06-04T04:52:06.501Z',
    time: '4:52 PM',
    type: 'Security',
    isNew: true,
  },
  {
    id: '2',
    title: 'New Delivery Options!',
    description:
      'We now offer multiple delivery options including express and same-day delivery. Try them out now.',
    date: '2024-06-04T04:52:06.501Z',
    time: '08:52 PM',
    type: 'Services',
    isNew: true,
  },
  {
    id: '3',
    title: 'App Update Available!',
    description:
      'Update our app now to get access to the latest features and improvements for a better delivery experience.',
    date: '2024-06-04T07:12:06.501Z',
    time: '07:12 AM',
    type: 'Update',
    isNew: false,
  },
  {
    id: '4',
    title: 'Delivery Scheduled!',
    description:
      'Your delivery has been successfully scheduled. Track your delivery for real-time updates.',
    date: '2024-06-04T11:14:06.501Z',
    time: '11:14 AM',
    type: 'Delivery',
    isNew: false,
  },
  {
    id: '5',
    title: 'Account Setup Successful!',
    description:
      'Your account creation is successful, you can now schedule and manage your deliveries.',
    date: '2024-06-03T08:39:06.501Z',
    time: '08:39 AM',
    type: 'Account',
    isNew: false,
  },
  {
    id: '6',
    title: 'Package Delivered!',
    description:
      'Your package has been successfully delivered. Check your delivery history for more details.',
    date: '2024-06-02T09:52:06.501Z',
    time: '09:52 AM',
    type: 'Delivery',
    isNew: false,
  },
  {
    id: '7',
    title: 'Scheduled Maintenance!',
    description:
      'Our app will undergo scheduled maintenance on June 10, 2024, from 02:00 AM to 04:00 AM.',
    date: '2024-06-01T03:22:06.501Z',
    time: '03:22 AM',
    type: 'Account',
    isNew: false,
  },
  {
    id: '8',
    title: 'New Pickup Locations!',
    description:
      'We now support new pickup locations in your area for your convenience.',
    date: '2024-05-30T06:15:06.501Z',
    time: '06:15 AM',
    type: 'Services',
    isNew: false,
  },
  {
    id: '9',
    title: 'Referral Bonus!',
    description:
      'Invite friends to use our app and earn up to $50 for each referral.',
    date: '2024-05-29T10:00:06.501Z',
    time: '10:00 AM',
    type: 'Promotion',
    isNew: false,
  },
  {
    id: '10',
    title: 'Password Change Successful!',
    description:
      'Your password has been changed successfully. If this was not you, please contact support immediately.',
    date: '2024-05-28T04:52:06.501Z',
    time: '04:52 AM',
    type: 'Security',
    isNew: false,
  },
];

export const messsagesData = [
  {
    id: '1',
    fullName: 'Jhon Smith',
    userImg: images.user1,
    lastSeen: '2023-11-16T04:52:06.501Z',
    lastMessage: 'I love you. see you soon baby',
    messageInQueue: 2,
    lastMessageTime: '12:25 PM',
    isOnline: true,
  },
  {
    id: '2',
    fullName: 'Anuska Sharma',
    userImg: images.user2,
    lastSeen: '2023-11-18T04:52:06.501Z',
    lastMessage: 'I Know. you are so busy man.',
    messageInQueue: 0,
    lastMessageTime: '12:15 PM',
    isOnline: false,
  },
  {
    id: '3',
    fullName: 'Virat Kohili',
    userImg: images.user3,
    lastSeen: '2023-11-20T04:52:06.501Z',
    lastMessage: 'Ok, see u soon',
    messageInQueue: 0,
    lastMessageTime: '09:12 PM',
    isOnline: true,
  },
  {
    id: '4',
    fullName: 'Shikhor Dhaon',
    userImg: images.user4,
    lastSeen: '2023-11-18T04:52:06.501Z',
    lastMessage: 'Great! Do you Love it.',
    messageInQueue: 0,
    lastMessageTime: '04:12 PM',
    isOnline: true,
  },
  {
    id: '5',
    fullName: 'Shakib Hasan',
    userImg: images.user5,
    lastSeen: '2023-11-21T04:52:06.501Z',
    lastMessage: 'Thank you !',
    messageInQueue: 2,
    lastMessageTime: '10:30 AM',
    isOnline: true,
  },
  {
    id: '6',
    fullName: 'Jacksoon',
    userImg: images.user6,
    lastSeen: '2023-11-20T04:52:06.501Z',
    lastMessage: 'Do you want to go out dinner',
    messageInQueue: 3,
    lastMessageTime: '10:05 PM',
    isOnline: false,
  },
  {
    id: '7',
    fullName: 'Tom Jerry',
    userImg: images.user7,
    lastSeen: '2023-11-20T04:52:06.501Z',
    lastMessage: 'Do you want to go out dinner',
    messageInQueue: 2,
    lastMessageTime: '11:05 PM',
    isOnline: true,
  },
  {
    id: '8',
    fullName: 'Lucky Luck',
    userImg: images.user8,
    lastSeen: '2023-11-20T04:52:06.501Z',
    lastMessage: 'Can you share the design with me?',
    messageInQueue: 2,
    lastMessageTime: '09:11 PM',
    isOnline: true,
  },
  {
    id: '9',
    fullName: 'Nate Jack',
    userImg: images.user9,
    lastSeen: '2023-11-20T04:52:06.501Z',
    lastMessage: 'Tell me what you want?',
    messageInQueue: 0,
    lastMessageTime: '06:43 PM',
    isOnline: true,
  },
];

export const callData = [
  {
    id: '1',
    fullName: 'Roselle Erhman',
    userImg: images.user10,
    status: 'Incoming',
    date: 'Dec 19, 2024',
  },
  {
    id: '2',
    fullName: 'Willard Purnell',
    userImg: images.user9,
    status: 'Outgoing',
    date: 'Dec 17, 2024',
  },
  {
    id: '3',
    fullName: 'Charlotte Hanlin',
    userImg: images.user8,
    status: 'Missed',
    date: 'Dec 16, 2024',
  },
  {
    id: '4',
    fullName: 'Merlin Kevin',
    userImg: images.user7,
    status: 'Missed',
    date: 'Dec 16, 2024',
  },
  {
    id: '5',
    fullName: 'Lavern Laboy',
    userImg: images.user6,
    status: 'Outgoing',
    date: 'Dec 16, 2024',
  },
  {
    id: '6',
    fullName: 'Phyllis Godley',
    userImg: images.user5,
    status: 'Incoming',
    date: 'Dec 15, 2024',
  },
  {
    id: '7',
    fullName: 'Tyra Dillon',
    userImg: images.user4,
    status: 'Outgoing',
    date: 'Dec 15, 2024',
  },
  {
    id: '8',
    fullName: 'Marci Center',
    userImg: images.user3,
    status: 'Missed',
    date: 'Dec 15, 2024',
  },
  {
    id: '9',
    fullName: 'Clinton Mccure',
    userImg: images.user2,
    status: 'Outgoing',
    date: 'Dec 15, 2024',
  },
];

export const transactionHistory = [
  {
    id: '1',
    title: 'New Order Made!',
    description: 'You have created a new shipping order',
    type: 'Order',
    date: '2024-06-16T04:52:06.501Z',
  },
  {
    id: '2',
    title: 'Top Up Successful!',
    description: 'You successfully top up your e-wallet for $700',
    type: 'Topup',
    date: '2024-06-15T04:52:06.501Z',
  },
  {
    id: '3',
    title: 'Payment Successful!',
    description: 'Shipping payment of $35 successfully made.',
    type: 'Payment',
    date: '2024-06-15T04:52:06.501Z',
  },
  {
    id: '4',
    title: 'Topup Successful!',
    description: 'You successfully top up your e-wallet for $5930',
    type: 'Topup',
    date: '2024-06-15T04:52:06.501Z',
  },
  {
    id: '5',
    title: 'New Order Made!',
    description: 'You have created a new shipping order',
    type: 'Order',
    date: '2024-06-15T04:52:06.501Z',
  },
  {
    id: '6',
    title: 'Payment Successful!',
    description: 'Shipping payment of $45 successfully made.',
    type: 'Payment',
    date: '2024-06-14T04:52:06.501Z',
  },
  {
    id: '7',
    title: 'New Order Made!',
    description: 'You have created a new shipping order',
    type: 'Order',
    date: '2024-06-14T04:52:06.501Z',
  },
  {
    id: '8',
    title: 'Top Up Successful!',
    description: 'You successfully top up your e-wallet for $3400',
    type: 'Topup',
    date: '2024-06-14T04:52:06.501Z',
  },
  {
    id: '9',
    title: 'E-Wallet Connected!',
    description: 'You have connected to the e-wallet',
    type: 'Payment',
    date: '2024-06-14T04:52:06.501Z',
  },
  {
    id: '10',
    title: 'New Order Made!',
    description: 'You have created a new shipping order',
    type: 'Order',
    date: '2024-06-14T04:52:06.501Z',
  },
];

export const TrackingHistory = [
  {
    id: '1',
    number: 'MM09130520',
    status: 'On Progress',
    description: 'On the way in delivery',
  },
  {
    id: '2',
    number: 'MM09130521',
    status: 'On Progress',
    description: 'On the way in delivery',
  },
  {
    id: '3',
    number: 'MM09130523',
    status: 'On Progress',
    description: 'On the way in delivery',
  },
  {
    id: '4',
    number: 'MM09130524',
    status: 'On Progress',
    description: 'On the way in delivery',
  },
  {
    id: '5',
    number: 'MM09130525',
    status: 'On Progress',
    description: 'On the way in delivery',
  },
  {
    id: '6',
    number: 'MM09130526',
    status: 'On Progress',
    description: 'On the way in delivery',
  },
  {
    id: '7',
    number: 'MM09130527',
    status: 'On Progress',
    description: 'On the way in delivery',
  },
  {
    id: '8',
    number: 'MM09130528',
    status: 'On Progress',
    description: 'On the way in delivery',
  },
  {
    id: '9',
    number: 'MM09130500',
    status: 'On Progress',
    description: 'On the way in delivery',
  },
];

export const deliveryReviews = [
  {
    id: '1',
    avatar: images.user1,
    name: 'John Smith',
    description:
      'The delivery was prompt and efficient! My package arrived in perfect condition, and the tracking updates were very helpful. Highly recommended! 😍',
    rating: 4.8,
    avgRating: 5,
    date: '2024-03-28T12:00:00.000Z',
    numLikes: 320,
  },
  {
    id: '2',
    avatar: images.user2,
    name: 'Emily Davis',
    description:
      'I was thoroughly impressed with the delivery service. The package arrived on time, and the delivery person was very courteous. Definitely using this service again!',
    rating: 4.7,
    avgRating: 5,
    date: '2024-03-28T12:00:00.000Z',
    numLikes: 95,
  },
  {
    id: '3',
    avatar: images.user3,
    name: 'Michael Rodriguez',
    description:
      'The delivery service exceeded my expectations! The package was delivered promptly, and the updates provided throughout the process were excellent. Will use this service again!',
    rating: 4.9,
    avgRating: 5,
    date: '2024-03-29T12:00:00.000Z',
    numLikes: 210,
  },
  {
    id: '4',
    avatar: images.user4,
    name: 'Sarah Brown',
    description:
      'I had a wonderful experience with the delivery service. The package arrived in great condition, and the delivery was very fast. Highly recommend using this service!',
    rating: 4.5,
    avgRating: 5,
    date: '2024-03-29T12:00:00.000Z',
    numLikes: 150,
  },
  {
    id: '5',
    avatar: images.user5,
    name: 'David Wilson',
    description:
      "Absolutely fantastic service! The delivery was quick and the package was handled with care. It's a must-try for anyone needing reliable delivery!",
    rating: 3.8,
    avgRating: 4,
    date: '2024-02-31T12:00:00.000Z',
    numLikes: 500,
  },
  {
    id: '6',
    avatar: images.user6,
    name: 'Luca Dalasias',
    description:
      'The delivery service exceeded my expectations! The package arrived on time, and the condition was perfect. Will definitely use this service again!',
    rating: 4.8,
    avgRating: 5,
    date: '2024-02-29T12:00:00.000Z',
    numLikes: 210,
  },
];

export const packageList = [
  {
    latitude: 48.8566,
    longitude: 2.3522,
    name: 'Package 1',
    description: 'Package description 2',
  },
  {
    latitude: 43.2965,
    longitude: 5.3698,
    name: 'Package 2',
    description: 'Package description 2',
  },
  {
    latitude: 45.764,
    longitude: 4.8357,
    name: 'Package 3',
    description: 'Package description 3',
  },
  {
    latitude: 43.6045,
    longitude: 1.4442,
    name: 'Package 4',
    description: 'Package description 4',
  },
  {
    latitude: 43.7102,
    longitude: 7.2661,
    name: 'Package 5',
    description: 'Package description 5',
  },
];

export const myOrders = [
  {
    id: 1,
    userId: 1,
    categoryId: 1,
    full_name: 'Hiren Surani',
    mobile: '971501234567',
    amount: '120.00',
    weight: '1.6',
    insured: true,
    pickup_lat: '25.276987',
    pickup_long: '55.296249',
    pickup_address: 'Dubai Mall, Downtown Dubai, Dubai, UAE',
    delivery_lat: '25.204849',
    delivery_long: '55.270782',
    delivery_address: 'Burj Khalifa, Downtown Dubai, Dubai, UAE',
    description: 'Laptop with bag',
    status: 'pending',
    createdAt: '2025-03-19T05:11:19.000Z',
    updatedAt: '2025-03-19T05:11:19.000Z',
    deliveryUser: {
      id: 101,
      name: 'John Doe',
      mobile: '971509876543',
    },
    media: [
      {
        id: 5,
        parcelId: 1,
        mediaUrl:
          'https://blog.papermart.com/wp-content/uploads/2020/12/2020_blog_HowToShipALaptop_600x400.jpg',
      },
      {
        id: 6,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?fm=jpg&q=60&w=3000',
      },
    ],
    category: {
      id: 1,
      name: 'Electronics',
      status: 'active',
    },
    pickup_distance: 3.2,
    parcel_distance: 4.1,
  },
  {
    id: 2,
    userId: 2,
    categoryId: 2,
    full_name: 'Amit Patel',
    mobile: '971502345678',
    amount: '90.00',
    weight: '0.8',
    insured: false,
    pickup_lat: '25.276987',
    pickup_long: '55.296249',
    pickup_address: 'Mall of the Emirates, Al Barsha, Dubai, UAE',
    delivery_lat: '25.188778',
    delivery_long: '55.272132',
    delivery_address: 'Jumeirah Beach, Dubai, UAE',
    description: 'Passport and documents',
    status: 'accepted',
    createdAt: '2025-03-19T06:15:10.000Z',
    updatedAt: '2025-03-19T06:15:10.000Z',
    deliveryUser: {
      id: 102,
      name: 'Michael Smith',
      mobile: '971508765432',
    },
    media: [
      {
        id: 7,
        parcelId: 2,
        mediaUrl:
          'https://5.imimg.com/data5/SELLER/Default/2024/4/*********/XH/DV/KW/*********/usa-passport.jpg',
      },
    ],
    category: {
      id: 2,
      name: 'Documents',
      status: 'active',
    },
    pickup_distance: 2.1,
    parcel_distance: 3.5,
  },
  {
    id: 3,
    userId: 3,
    categoryId: 3,
    full_name: 'Rahul Mehta',
    mobile: '971503456789',
    amount: '150.00',
    weight: '2.0',
    insured: true,
    pickup_lat: '25.335773',
    pickup_long: '55.365180',
    pickup_address: 'Deira City Center, Deira, Dubai, UAE',
    delivery_lat: '25.276987',
    delivery_long: '55.337114',
    delivery_address: 'Al Rigga, Deira, Dubai, UAE',
    description: 'Books and study material',
    status: 'picked',
    createdAt: '2025-03-19T07:22:30.000Z',
    updatedAt: '2025-03-19T07:22:30.000Z',
    deliveryUser: {
      id: 103,
      name: 'Sophia Williams',
      mobile: '971507654321',
    },
    media: [
      {
        id: 8,
        parcelId: 3,
        mediaUrl:
          'https://bodheeprep.com/wp-content/uploads/2023/01/CAT-Study-material-pdf-free-download.jpg',
      },
    ],
    category: {
      id: 3,
      name: 'Books',
      status: 'active',
    },
    pickup_distance: 3.9,
    parcel_distance: 5.0,
  },
  {
    id: 4,
    userId: 4,
    categoryId: 4,
    full_name: 'Sanjay Verma',
    mobile: '971504567890',
    amount: '60.00',
    weight: '1.2',
    insured: false,
    pickup_lat: '25.066488',
    pickup_long: '55.144555',
    pickup_address: 'Palm Jumeirah, Dubai, UAE',
    delivery_lat: '25.078218',
    delivery_long: '55.132216',
    delivery_address: 'Atlantis The Palm, Dubai, UAE',
    description: 'Clothes and accessories',
    status: 'delivered',
    createdAt: '2025-03-19T08:45:00.000Z',
    updatedAt: '2025-03-19T08:45:00.000Z',
    deliveryUser: {
      id: 104,
      name: 'Emma Brown',
      mobile: '971506543210',
    },
    media: [
      {
        id: 9,
        parcelId: 4,
        mediaUrl:
          'https://bsmedia.business-standard.com/_media/bs/img/article/2021-11/05/full/1636094253-2135.jpg',
      },
    ],
    category: {
      id: 4,
      name: 'Clothing',
      status: 'active',
    },
    pickup_distance: 2.5,
    parcel_distance: 3.6,
  },
];

export const locations = [
  {
    id: 1,
    userId: 1,
    categoryId: 1,
    full_name: 'Hiren Surani',
    mobile: '971501234567',
    amount: '120.00',
    weight: '1.6',
    insured: true,
    pickup_lat: '25.276987',
    pickup_long: '55.296249',
    pickup_address: 'Dubai Mall, Downtown Dubai, Dubai, UAE',
    delivery_lat: '25.204849',
    delivery_long: '55.270782',
    delivery_address: 'Burj Khalifa, Downtown Dubai, Dubai, UAE',
    description: 'Laptop with bag',
    status: 'pending',
    createdAt: '2025-03-19T05:11:19.000Z',
    updatedAt: '2025-03-19T05:11:19.000Z',
    media: [
      {
        id: 5,
        parcelId: 1,
        mediaUrl:
          'https://blog.papermart.com/wp-content/uploads/2020/12/2020_blog_HowToShipALaptop_600x400.jpg',
      },
      {
        id: 6,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGxhcHRvcCUyMGNvbXB1dGVyfGVufDB8fDB8fHww',
      },
      {
        id: 7,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=2944&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      },
    ],
    category: {
      id: 1,
      name: 'Electronics',
      status: 'active',
    },
    pickup_distance: 3.2,
    parcel_distance: 4.1,
  },
  {
    id: 2,
    userId: 2,
    categoryId: 2,
    full_name: 'Amit Patel',
    mobile: '971502345678',
    amount: '90.00',
    weight: '0.8',
    insured: false,
    pickup_lat: '25.276987',
    pickup_long: '55.296249',
    pickup_address: 'Mall of the Emirates, Al Barsha, Dubai, UAE',
    delivery_lat: '25.188778',
    delivery_long: '55.272132',
    delivery_address: 'Jumeirah Beach, Dubai, UAE',
    description: 'Passport and documents',
    status: 'in-transit',
    createdAt: '2025-03-19T06:15:10.000Z',
    updatedAt: '2025-03-19T06:15:10.000Z',
    media: [
      {
        id: 5,
        parcelId: 1,
        mediaUrl:
          'https://5.imimg.com/data5/SELLER/Default/2024/4/*********/XH/DV/KW/*********/usa-passport.jpg',
      },
      {
        id: 6,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGxhcHRvcCUyMGNvbXB1dGVyfGVufDB8fDB8fHww',
      },
      {
        id: 7,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=2944&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      },
    ],
    category: {
      id: 2,
      name: 'Documents',
      status: 'active',
    },
    pickup_distance: 2.1,
    parcel_distance: 3.5,
  },
  {
    id: 3,
    userId: 3,
    categoryId: 3,
    full_name: 'Rahul Mehta',
    mobile: '971503456789',
    amount: '150.00',
    weight: '2.0',
    insured: true,
    pickup_lat: '25.335773',
    pickup_long: '55.365180',
    pickup_address: 'Deira City Center, Deira, Dubai, UAE',
    delivery_lat: '25.276987',
    delivery_long: '55.337114',
    delivery_address: 'Al Rigga, Deira, Dubai, UAE',
    description: 'Books and study material',
    status: 'pending',
    createdAt: '2025-03-19T07:22:30.000Z',
    updatedAt: '2025-03-19T07:22:30.000Z',
    media: [
      {
        id: 5,
        parcelId: 1,
        mediaUrl:
          'https://thumbs.dreamstime.com/z/studying-books-learning-materials-notebook-blackboard-book-media-teaching-pencils-learning-materials-educational-concepts-118557516.jpg',
      },
      {
        id: 6,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGxhcHRvcCUyMGNvbXB1dGVyfGVufDB8fDB8fHww',
      },
      {
        id: 7,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=2944&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      },
    ],
    category: {
      id: 3,
      name: 'Books',
      status: 'active',
    },
    pickup_distance: 3.9,
    parcel_distance: 5.0,
  },
  {
    id: 4,
    userId: 4,
    categoryId: 4,
    full_name: 'Sanjay Verma',
    mobile: '971504567890',
    amount: '60.00',
    weight: '1.2',
    insured: false,
    pickup_lat: '25.066488',
    pickup_long: '55.144555',
    pickup_address: 'Palm Jumeirah, Dubai, UAE',
    delivery_lat: '25.078218',
    delivery_long: '55.132216',
    delivery_address: 'Atlantis The Palm, Dubai, UAE',
    description: 'Clothes and accessories',
    status: 'pending',
    createdAt: '2025-03-19T08:45:00.000Z',
    updatedAt: '2025-03-19T08:45:00.000Z',
    media: [
      {
        id: 5,
        parcelId: 1,
        mediaUrl:
          'https://bsmedia.business-standard.com/_media/bs/img/article/2021-11/05/full/1636094253-2135.jpg?im=FeatureCrop,size=(826,465)',
      },
      {
        id: 6,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGxhcHRvcCUyMGNvbXB1dGVyfGVufDB8fDB8fHww',
      },
      {
        id: 7,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=2944&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      },
    ],
    category: {
      id: 4,
      name: 'Clothing',
      status: 'active',
    },
    pickup_distance: 2.5,
    parcel_distance: 3.6,
  },
  {
    id: 5,
    userId: 5,
    categoryId: 5,
    full_name: 'Rohan Sharma',
    mobile: '971505678901',
    amount: '200.00',
    weight: '5.0',
    insured: true,
    pickup_lat: '25.263272',
    pickup_long: '55.308938',
    pickup_address: 'Business Bay, Dubai, UAE',
    delivery_lat: '25.285447',
    delivery_long: '55.385573',
    delivery_address: 'Dubai Creek Harbour, Dubai, UAE',
    description: 'Mobile Phone',
    status: 'shipped',
    createdAt: '2025-03-19T10:10:45.000Z',
    updatedAt: '2025-03-19T10:10:45.000Z',
    media: [
      {
        id: 5,
        parcelId: 1,
        mediaUrl:
          'https://img.freepik.com/free-photo/white-cell-phone-box-background_58702-4709.jpg',
      },
      {
        id: 6,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGxhcHRvcCUyMGNvbXB1dGVyfGVufDB8fDB8fHww',
      },
      {
        id: 7,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=2944&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      },
    ],
    category: {
      id: 5,
      name: 'Electronics',
      status: 'active',
    },
    pickup_distance: 4.1,
    parcel_distance: 5.7,
  },
  {
    id: 6,
    userId: 6,
    categoryId: 6,
    full_name: 'Neha Kapoor',
    mobile: '971506789012',
    amount: '80.00',
    weight: '1.5',
    insured: true,
    pickup_lat: '25.230928',
    pickup_long: '55.286374',
    pickup_address: 'Al Safa Park, Dubai, UAE',
    delivery_lat: '25.245204',
    delivery_long: '55.308216',
    delivery_address: 'Dubai Opera, Downtown Dubai, Dubai, UAE',
    description: 'Perfume and cosmetics',
    status: 'delivered',
    createdAt: '2025-03-19T11:30:00.000Z',
    updatedAt: '2025-03-19T11:30:00.000Z',
    media: [
      {
        id: 5,
        parcelId: 1,
        mediaUrl:
          'https://media.istockphoto.com/id/1165365435/photo/cosmetics.jpg?s=612x612&w=0&k=20&c=RMMBnP6JvBBvBGAM8P5q31i2GZDKQ42YfkRlK-KCnGw=',
      },
      {
        id: 6,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1611186871348-b1ce696e52c9?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGxhcHRvcCUyMGNvbXB1dGVyfGVufDB8fDB8fHww',
      },
      {
        id: 7,
        parcelId: 1,
        mediaUrl:
          'https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=2944&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      },
    ],
    category: {
      id: 6,
      name: 'Cosmetics',
      status: 'active',
    },
    pickup_distance: 3.4,
    parcel_distance: 4.9,
  },
];

export const outgoingShipments = [
  {
    id: 1,
    status: 'Shipped',
    date: '29 Jan, 12:30',
    type: 'Electronics',
    name: 'Gadget Hub',
    image: images.package1,
    price: 499.99,
    numberOfItems: 1,
    trackingNumber: '#162432',
    address: '123 Main St, Cityville',
    receipt: '#A142C',
  },
  {
    id: 2,
    status: 'Shipped',
    date: '30 Jan, 12:30',
    type: 'Appliances',
    name: 'HomeTech Solutions',
    image: images.package2,
    price: 899.99,
    numberOfItems: 1,
    trackingNumber: '#162422',
    address: '456 Oak St, Townsville',
    receipt: '#12AXD',
  },
  {
    id: 3,
    status: 'Canceled',
    date: '30 Jan, 12:30',
    type: 'Gaming',
    name: 'Game Zone Express',
    image: images.package3,
    price: 299.99,
    numberOfItems: 1,
    trackingNumber: '#232432',
    address: '789 Pine St, Villagetown',
    receipt: '#XD234',
  },
  {
    id: 4,
    status: 'Shipped',
    date: '29 Jan, 12:30',
    type: 'Electronics',
    name: 'Gadget Hub',
    image: images.package4,
    price: 499.99,
    numberOfItems: 1,
    trackingNumber: '#162432',
    address: '123 Main St, Cityville',
    receipt: '#A1424',
  },
  {
    id: 5,
    status: 'Shipped',
    date: '30 Jan, 12:30',
    type: 'Appliances',
    name: 'HomeTech Solutions',
    image: images.package5,
    price: 899.99,
    numberOfItems: 1,
    trackingNumber: '#162422',
    address: '456 Oak St, Townsville',
    receipt: '#12AX2',
  },
  {
    id: 6,
    status: 'Canceled',
    date: '30 Jan, 12:30',
    type: 'Gaming',
    name: 'Game Zone Express',
    image: images.package6,
    price: 299.99,
    numberOfItems: 1,
    trackingNumber: '#232432',
    address: '789 Pine St, Villagetown',
    receipt: '#FS234',
  },
];

export const incomingShipments = [
  {
    id: 1,
    status: 'Delivered',
    date: '29 Jan, 12:30',
    type: 'Electronics',
    name: 'Gadget Hub',
    image: images.package3,
    price: 499.99,
    numberOfItems: 1,
    trackingNumber: '#162432',
    address: 'Your Address',
    receipt: '#43X56',
  },
  {
    id: 2,
    status: 'Delivered',
    date: '30 Jan, 12:30',
    type: 'Appliances',
    name: 'HomeTech Solutions',
    image: images.package4,
    price: 899.99,
    numberOfItems: 1,
    trackingNumber: '#162422',
    address: 'Your Address',
    receipt: '#43AZ2',
  },
  {
    id: 3,
    status: 'Canceled',
    date: '30 Jan, 12:30',
    type: 'Gaming',
    name: 'Game Zone Express',
    image: images.package5,
    price: 299.99,
    numberOfItems: 1,
    trackingNumber: '#232432',
    address: 'Your Address',
    receipt: '#43012',
  },
  {
    id: 4,
    status: 'Delivered',
    date: '29 Jan, 12:30',
    type: 'Electronics',
    name: 'Gadget Hub',
    image: images.package1,
    price: 499.99,
    numberOfItems: 1,
    trackingNumber: '#162432',
    address: 'Your Address',
    receipt: '#43X56',
  },
  {
    id: 5,
    status: 'Delivered',
    date: '30 Jan, 12:30',
    type: 'Appliances',
    name: 'HomeTech Solutions',
    image: images.package2,
    price: 899.99,
    numberOfItems: 1,
    trackingNumber: '#162422',
    address: 'Your Address',
    receipt: '#43AZ2',
  },
  {
    id: 6,
    status: 'Canceled',
    date: '30 Jan, 12:30',
    type: 'Gaming',
    name: 'Game Zone Express',
    image: images.package7,
    price: 299.99,
    numberOfItems: 1,
    trackingNumber: '#232432',
    address: 'Your Address',
    receipt: '#43012',
  },
];
