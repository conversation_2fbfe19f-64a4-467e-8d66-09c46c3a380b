<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location to provide better service.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We need your location to provide better service.</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Saska</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
		<key>NSCameraUsageDescription</key>
		<string>$(PRODUCT_NAME) needs access to your Camera.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>$(PRODUCT_NAME) needs access to your Microphone.</string>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIAppFonts</key>
	<array>
		<string>Urbanist Black.ttf</string>
		<string>Urbanist BlackItalic.ttf</string>
		<string>Urbanist Bold.ttf</string>
		<string>Urbanist BoldItalic.ttf</string>
		<string>Urbanist ExtraBold.ttf</string>
		<string>Urbanist ExtraBoldItalic.ttf</string>
		<string>Urbanist ExtraLight.ttf</string>
		<string>Urbanist ExtraLightItalic.ttf</string>
		<string>Urbanist Italic.ttf</string>
		<string>Urbanist Light.ttf</string>
		<string>Urbanist LightItalic.ttf</string>
		<string>Urbanist Medium.ttf</string>
		<string>Urbanist MediumItalic.ttf</string>
		<string>Urbanist Regular.ttf</string>
		<string>Urbanist SemiBold.ttf</string>
		<string>Urbanist SemiBoldItalic.ttf</string>
		<string>Urbanist Thin.ttf</string>
		<string>Urbanist ThinItalic.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>FontAwesome6_Brands.ttf</string>
		<string>FontAwesome6_Regular.ttf</string>
		<string>FontAwesome6_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Fontisto.ttf</string>
		<string>SpaceMono-Regular.ttf</string>
	</array>
</dict>
</plist>
