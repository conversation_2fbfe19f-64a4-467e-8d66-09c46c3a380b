import {SafeAreaProvider} from 'react-native-safe-area-context';
import {LogBox} from 'react-native';
import {ThemeProvider} from './theme/ThemeProvider';
import AppNavigation from './navigations/AppNavigation';
import {Providers} from './redux/provider';

//Ignore all log notifications
LogBox.ignoreAllLogs();

export default function App() {
  return (
    <ThemeProvider>
      <Providers>
        <SafeAreaProvider>
          <AppNavigation />
        </SafeAreaProvider>
      </Providers>
    </ThemeProvider>
  );
}
