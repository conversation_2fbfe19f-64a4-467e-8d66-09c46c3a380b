import {View, Platform, Image, Text, ImageSourcePropType} from 'react-native';
import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {COLORS, FONTS, icons} from '../constants';
import {useTheme} from '../theme/ThemeProvider';
import {Home, Inbox, MyOrder, NearbyDrop, Profile} from '../screens';

const Tab = createBottomTabNavigator();

const BottomTabNavigation = () => {
  const {dark} = useTheme();

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarShowLabel: false,
        headerShown: false,
        tabBarStyle: {
          position: 'absolute',
          justifyContent: 'center',
          bottom: 0,
          right: 0,
          left: 0,
          elevation: 0,
          height: Platform.OS === 'ios' ? 90 : 60,
          backgroundColor: dark ? COLORS.dark1 : COLORS.white,
          borderTopColor: 'transparent',
        },
      }}>
      <Tab.Screen
        name="Home"
        component={Home}
        options={{
          tabBarIcon: ({focused}: {focused: boolean}) => {
            return (
              <View
                style={{
                  alignItems: 'center',
                  paddingTop: 16,
                }}>
                <Image
                  source={
                    focused
                      ? (icons.home4 as ImageSourcePropType)
                      : (icons.home4Outline as ImageSourcePropType)
                  }
                  resizeMode="contain"
                  style={{
                    width: 24,
                    height: 24,
                    tintColor: focused
                      ? COLORS.primary
                      : dark
                      ? COLORS.gray3
                      : COLORS.gray3,
                  }}
                />
                <Text
                  style={{
                    ...FONTS.body4,
                    color: focused
                      ? COLORS.primary
                      : dark
                      ? COLORS.gray3
                      : COLORS.gray3,
                  }}>
                  Home
                </Text>
              </View>
            );
          },
        }}
      />
      <Tab.Screen
        name="Marketplace"
        component={NearbyDrop}
        options={{
          tabBarIcon: ({focused}: {focused: boolean}) => {
            return (
              <View
                style={{
                  alignItems: 'center',
                  paddingTop: 16,
                }}>
                <Image
                  source={
                    focused
                      ? (icons.document as ImageSourcePropType)
                      : (icons.documentOutline as ImageSourcePropType)
                  }
                  resizeMode="contain"
                  style={{
                    width: 24,
                    height: 24,
                    tintColor: focused
                      ? COLORS.primary
                      : dark
                      ? COLORS.gray3
                      : COLORS.gray3,
                  }}
                />
                <Text
                  style={{
                    ...FONTS.body4,
                    color: focused
                      ? COLORS.primary
                      : dark
                      ? COLORS.gray3
                      : COLORS.gray3,
                  }}>
                  Marketplace
                </Text>
              </View>
            );
          },
        }}
      />
      <Tab.Screen
        name="MyOrder"
        component={MyOrder}
        options={{
          tabBarIcon: ({focused}: {focused: boolean}) => {
            return (
              <View
                style={{
                  alignItems: 'center',
                  paddingTop: 16,
                }}>
                <Image
                  source={
                    focused
                      ? (icons.document as ImageSourcePropType)
                      : (icons.documentOutline as ImageSourcePropType)
                  }
                  resizeMode="contain"
                  style={{
                    width: 24,
                    height: 24,
                    tintColor: focused
                      ? COLORS.primary
                      : dark
                      ? COLORS.gray3
                      : COLORS.gray3,
                  }}
                />
                <Text
                  style={{
                    ...FONTS.body4,
                    color: focused
                      ? COLORS.primary
                      : dark
                      ? COLORS.gray3
                      : COLORS.gray3,
                  }}>
                  My Parcel
                </Text>
              </View>
            );
          },
        }}
      />
      <Tab.Screen
        name="Inbox"
        component={Inbox}
        options={{
          tabBarIcon: ({focused}: {focused: boolean}) => {
            return (
              <View
                style={{
                  alignItems: 'center',
                  paddingTop: 16,
                }}>
                <Image
                  source={
                    focused
                      ? (icons.chatBubble2 as ImageSourcePropType)
                      : (icons.chatBubble2Outline as ImageSourcePropType)
                  }
                  resizeMode="contain"
                  style={{
                    width: 24,
                    height: 24,
                    tintColor: focused
                      ? COLORS.primary
                      : dark
                      ? COLORS.gray3
                      : COLORS.gray3,
                  }}
                />
                <Text
                  style={{
                    ...FONTS.body4,
                    color: focused
                      ? COLORS.primary
                      : dark
                      ? COLORS.gray3
                      : COLORS.gray3,
                  }}>
                  Inbox
                </Text>
              </View>
            );
          },
        }}
      />
      <Tab.Screen
        name="Profile"
        component={Profile}
        options={{
          tabBarIcon: ({focused}: {focused: boolean}) => {
            return (
              <View
                style={{
                  alignItems: 'center',
                  paddingTop: 16,
                }}>
                <Image
                  source={
                    focused
                      ? (icons.profile2 as ImageSourcePropType)
                      : (icons.profile2Outline as ImageSourcePropType)
                  }
                  resizeMode="contain"
                  style={{
                    width: 24,
                    height: 24,
                    tintColor: focused
                      ? COLORS.primary
                      : dark
                      ? COLORS.gray3
                      : COLORS.gray3,
                  }}
                />
                <Text
                  style={{
                    ...FONTS.body4,
                    color: focused
                      ? COLORS.primary
                      : dark
                      ? COLORS.gray3
                      : COLORS.gray3,
                  }}>
                  Profile
                </Text>
              </View>
            );
          },
        }}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigation;
