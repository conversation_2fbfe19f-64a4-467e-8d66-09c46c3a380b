import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { NavigationContainer } from '@react-navigation/native';
import React from 'react';
import { AddNewAddress, AddNewCard, Address, Call, ChangeEmail, ChangePIN, ChangePassword, Chat, CheckRates, CreateNewPIN, CreateNewPassword, CustomerService, EReceipt, EditProfile, FaceRecognitionScan, FaceRecognitionWalkthrough, FillYourProfile, Fingerprint, ForgotPasswordEmail, ForgotPasswordMethods, ForgotPasswordPhoneNumber, GiveTipForCourier, Home, Inbox, Login, MyOrder, MyOrderTrack, NearbyDrop, Notifications, OTPVerification, Onboarding, Onboarding2, Onboarding3, Onboarding4, OrderDetails, OrderForm, PhotoIdCard, Profile, ProofOfResidency, RateTheCourier, ReasonForUsingSaska, ScanQrCode, SelfieWithIdCard, SettingsHelpCenter, SettingsInviteFriends, SettingsLanguage, SettingsNotifications, SettingsPayment, SettingsPrivacyPolicy, SettingsSecurity, Signup, TopupAmount, TopupConfirmPIN, TopupEReceipt, TopupMethods, TrackIdNumber, TrackingPackage, TransactionHistory, VerifyYourIdentity, Welcome, WhatsYourMood, WriteReview } from '../screens';
import BottomTabNavigation from './BottomTabNavigation';
import Splash from '../screens/Splash';
import ParcelDetail from '../screens/ParcelDetails';
import ParcelLive from '../screens/ParcelLive';
// import BottomTabNavigation from './BottomTabNavigation';

const Stack = createNativeStackNavigator();

const AppNavigation = () => {

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{ headerShown: false }}
        // replace the second onboaring1 with login in order to make the user not to see the onboarding 
        // when login the next time
        initialRouteName={'splash'}>
        <Stack.Screen name="splash" component={Splash} />
        <Stack.Screen name="addnewaddress" component={AddNewAddress} />
        <Stack.Screen name="addnewcard" component={AddNewCard} />
        <Stack.Screen name="address" component={Address} />
        <Stack.Screen name="call" component={Call} />
        <Stack.Screen name="changeemail" component={ChangeEmail} />
        <Stack.Screen name="changepassword" component={ChangePassword} />
        <Stack.Screen name="changepin" component={ChangePIN} />
        <Stack.Screen name="chat" component={Chat} />
        <Stack.Screen name="checkrates" component={CheckRates} />
        <Stack.Screen name="createnewpassword" component={CreateNewPassword} />
        <Stack.Screen name="createnewpin" component={CreateNewPIN} />
        <Stack.Screen name="customerservice" component={CustomerService} />
        <Stack.Screen name="editprofile" component={EditProfile} />
        <Stack.Screen name="ereceipt" component={EReceipt} />
        <Stack.Screen name="facerecognitionscan" component={FaceRecognitionScan} />
        <Stack.Screen name="facerecognitionwalkthrough" component={FaceRecognitionWalkthrough} />
        <Stack.Screen name="fillyourprofile" component={FillYourProfile} />
        <Stack.Screen name="fingerprint" component={Fingerprint} />
        <Stack.Screen name="forgotpasswordemail" component={ForgotPasswordEmail} />
        <Stack.Screen name="forgotpasswordmethods" component={ForgotPasswordMethods} />
        <Stack.Screen name="forgotpasswordphonenumber" component={ForgotPasswordPhoneNumber} />
        <Stack.Screen name="givetipforcourier" component={GiveTipForCourier} />
        <Stack.Screen name="home" component={Home} />
        <Stack.Screen name="inbox" component={Inbox} />
        <Stack.Screen name="login" component={Login} />
        <Stack.Screen name="myorder" component={MyOrder} />
        <Stack.Screen name="myordertrack" component={MyOrderTrack} />
        <Stack.Screen name="nearbydrop" component={NearbyDrop} />
        <Stack.Screen name="notifications" component={Notifications} />
        <Stack.Screen name="onboarding1" component={Onboarding} />
        <Stack.Screen name="onboarding2" component={Onboarding2} />
        <Stack.Screen name="onboarding3" component={Onboarding3} />
        <Stack.Screen name="onboarding4" component={Onboarding4} />
        <Stack.Screen name="orderdetails" component={OrderDetails} />
        <Stack.Screen name="parceldetails" component={ParcelDetail} />
        <Stack.Screen name="parcellive" component={ParcelLive} />
        <Stack.Screen name="orderform" component={OrderForm} />
        <Stack.Screen name="otpverification" component={OTPVerification} />
        <Stack.Screen name="photoidcard" component={PhotoIdCard} />
        <Stack.Screen name="profile" component={Profile} />
        <Stack.Screen name="proofofresidency" component={ProofOfResidency} />
        <Stack.Screen name="ratethecourier" component={RateTheCourier} />
        <Stack.Screen name="reasonforusingsaska" component={ReasonForUsingSaska} />
        <Stack.Screen name="scanqrcode" component={ScanQrCode} />
        <Stack.Screen name="selfiewithidcard" component={SelfieWithIdCard} />
        <Stack.Screen name="settingshelpcenter" component={SettingsHelpCenter} />
        <Stack.Screen name="settingsinvitefriends" component={SettingsInviteFriends} />
        <Stack.Screen name="settingslanguage" component={SettingsLanguage} />
        <Stack.Screen name="settingsnotifications" component={SettingsNotifications} />
        <Stack.Screen name="settingspayment" component={SettingsPayment} />
        <Stack.Screen name="settingsprivacypolicy" component={SettingsPrivacyPolicy} />
        <Stack.Screen name="settingssecurity" component={SettingsSecurity} />
        <Stack.Screen name="signup" component={Signup} />
        <Stack.Screen name="topupamount" component={TopupAmount} />
        <Stack.Screen name="topupconfirmpin" component={TopupConfirmPIN} />
        <Stack.Screen name="topupereceipt" component={TopupEReceipt} />
        <Stack.Screen name="topupmethods" component={TopupMethods} />
        <Stack.Screen name="trackidnumber" component={TrackIdNumber} />
        <Stack.Screen name="trackingpackage" component={TrackingPackage} />
        <Stack.Screen name="transactionhistory" component={TransactionHistory} />
        <Stack.Screen name="verifyyouridentity" component={VerifyYourIdentity} />
        <Stack.Screen name="welcome" component={Welcome} />
        <Stack.Screen name="whatsyourmood" component={WhatsYourMood} />
        <Stack.Screen name="writereview" component={WriteReview} />
        <Stack.Screen name="Main" component={BottomTabNavigation} />
      </Stack.Navigator>
    </NavigationContainer>
  )
}

export default AppNavigation