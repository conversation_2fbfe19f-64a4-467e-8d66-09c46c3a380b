{"name": "Saska", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@kichiyaki/react-native-barcode-generator": "^0.6.7", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/slider": "^4.5.2", "@react-native-picker/picker": "^2.7.6", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@reduxjs/toolkit": "^2.6.0", "axios": "^1.8.1", "i": "^0.3.7", "moment": "^2.30.1", "npm": "^10.8.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-chart-kit": "^6.12.0", "react-native-element-dropdown": "^2.12.4", "react-native-fingerprint-scanner": "git+https://github.com/hieuvp/react-native-fingerprint-scanner.git#9cecc0db326471c571553ea85f7c016fee2f803d", "react-native-geocoding": "^0.5.0", "react-native-geolocation-service": "^5.3.1", "react-native-get-random-values": "^1.11.0", "react-native-gifted-chat": "^2.4.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-image-picker": "^7.1.2", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.15.3", "react-native-maps-directions": "^1.9.0", "react-native-modern-datepicker": "^1.0.0-beta.91", "react-native-otp-entry": "^1.7.0", "react-native-pager-view": "^6.3.1", "react-native-picker-select": "^9.1.3", "react-native-qrcode-svg": "^6.3.1", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-safe-area-context": "^4.10.4", "react-native-screens": "^3.31.1", "react-native-svg": "^15.3.0", "react-native-tab-view": "^3.5.2", "react-native-vector-icons": "^10.1.0", "react-native-virtualized-view": "^1.0.0", "react-native-vision-camera": "^3.9.2", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "validate.js": "^0.13.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-native-modern-datepicker": "^1.0.5", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}