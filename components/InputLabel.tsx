import { View, Text, StyleSheet } from 'react-native';
import React from 'react';
import { COLORS } from '../constants';
import { useTheme } from '../theme/ThemeProvider';

interface InputLabelProps {
  title: string;
}

const InputLabel: React.FC<InputLabelProps> = ({ title }) => {
  const { dark } = useTheme();

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { 
        color: dark ? COLORS.white : COLORS.black
      }]}>{title}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 12,
  },
  title: {
    fontSize: 16,
    fontFamily: "Urbanist Bold",
    color: COLORS.black,
  },
});

export default InputLabel;
