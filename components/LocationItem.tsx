import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import React from 'react';
import {COLORS, SIZES, icons} from '../constants';
import {useTheme} from '../theme/ThemeProvider';

interface LocationItemProps {
  description: string;
  distance: string;
  parcelDistance: string;
  image: string;
  onPress: () => void;
  amount: string;
}

const LocationItem: React.FC<LocationItemProps> = ({
  description,
  distance,
  onPress,
  image,
  amount,
  parcelDistance,
}) => {
  const {dark} = useTheme();

  return (
    <TouchableOpacity onPress={onPress} style={styles.itemContainer}>
      {image && <Image source={{uri: image}} style={styles.itemImage} />}

      <View style={styles.contentContainer}>
        <Text
          style={[
            styles.location,
            {color: dark ? COLORS.white : COLORS.black},
          ]}>
          {description}
        </Text>
        <View style={styles.infoItem}>
          <Text style={styles.label}>Pickup:</Text>
          <Text style={styles.value}>{distance}</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.label}>Dropoff:</Text>
          <Text style={styles.value}>{parcelDistance}</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={[styles.label, {marginTop: 3}]}>AED:</Text>
          <Text
            style={[
              styles.value,
              {
                color: COLORS.primary,
              },
            ]}>
            {amount}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row', // Row layout
    alignItems: 'center', // Align items in center
    padding: 6,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginVertical: 4,
    marginHorizontal: 8,
  },
  itemImage: {
    width: 80, // Smaller image
    height: 80,
    borderRadius: 10,
    marginRight: 10, // Space between image & details
  },
  contentContainer: {
    flex: 1, // Take up remaining space
    justifyContent: 'space-between',
  },
  location: {
    fontSize: 14,
    fontFamily: 'Urbanist SemiBold',
  },
  address: {
    fontSize: 12,
    fontFamily: 'Urbanist Regular',
    color: COLORS.gray,
    marginBottom: 4,
  },
  infoContainer: {
    flexDirection: 'row', // Display info in a row
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontSize: 12,
    fontFamily: 'Urbanist Medium',
    color: COLORS.gray,
  },
  value: {
    fontSize: 12,
    marginTop: 3,
    flex: 1,
    fontFamily: 'Urbanist Bold',
    marginLeft: 3,
  },
});

export default LocationItem;
