import { View, Text, StyleSheet, Image, TouchableOpacity, Platform, GestureResponderEvent } from 'react-native';
import React from 'react';
import { COLORS } from '../constants';
import { useTheme } from '../theme/ThemeProvider';

interface PaymentMethodItemConnectedProps {
  onPress: (event: GestureResponderEvent) => void;
  title: string;
  icon: any;
  tintColor?: string;
}

const PaymentMethodItemConnected: React.FC<PaymentMethodItemConnectedProps> = ({
  onPress,
  title,
  icon,
  tintColor
}) => {
  const { dark } = useTheme();

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.container,
      Platform.OS === 'android' ? styles.androidShadow : styles.iosShadow,
      { backgroundColor: dark ? COLORS.dark2 : COLORS.white }]}>
      <View style={styles.rightContainer}>
        <Image
          source={icon}
          resizeMode='contain'
          style={[styles.icon, {
            tintColor: tintColor
          }]}
        />
        <View>
          <Text style={[styles.title, {
            color: dark ? COLORS.white : COLORS.greyscale900
          }]}>{title}</Text>
        </View>
      </View>
      <View style={styles.leftContainer}>
        <Text style={styles.connectedTitle}>Connected</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: 16,
    paddingVertical: 12,
    paddingHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    height: 76,
    backgroundColor: COLORS.white,
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    height: 26,
    width: 26,
    marginRight: 16,
  },
  title: {
    fontSize: 18,
    fontFamily: "Urbanist Bold",
    color: COLORS.greyscale900,
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roundedChecked: {
    width: 20,
    height: 20,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  roundedCheckedCheck: {
    height: 10,
    width: 10,
    backgroundColor: COLORS.primary,
    borderRadius: 999,
  },
  androidShadow: {
    elevation: 1,
  },
  iosShadow: {
    shadowColor: 'rgba(4, 6, 15, 0.05)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
  },
  connectedTitle: {
    fontSize: 16,
    fontFamily: "Urbanist SemiBold",
    color: COLORS.primary
  }
});

export default PaymentMethodItemConnected;