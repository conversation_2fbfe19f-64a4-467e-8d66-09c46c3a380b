import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Alert} from 'react-native';
import Svg, {Circle} from 'react-native-svg';
import {COLORS} from '../constants';

interface ParcelDeliveryTimerProps {
  initialTime?: number;
  acceptedAt?: string;
  onReturnToMarketplace: () => void;
  showExtend?: boolean;
}

const CIRCLE_SIZE = 65;
const STROKE_WIDTH = 6;
const DEFAULT_PICKUP_TIME_MINUTES = 15; // 15 minutes default pickup time

const ParcelDeliveryTimer: React.FC<ParcelDeliveryTimerProps> = ({
  initialTime = 900,
  acceptedAt,
  onReturnToMarketplace,
  showExtend = true
}) => {
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [extensions, setExtensions] = useState<number>(0);
  const [totalTime, setTotalTime] = useState<number>(0);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  // Initialize timer based on accepted_at timestamp or fallback to initialTime
  useEffect(() => {
    if (isInitialized) return;

    let calculatedTime = initialTime;
    let calculatedTotalTime = initialTime;

    if (acceptedAt) {
      // Convert UTC accepted_at to local time and calculate remaining time
      const acceptedTime = new Date(acceptedAt);
      const currentTime = new Date();
      const timeDifference = currentTime.getTime() - acceptedTime.getTime();
      const elapsedSeconds = Math.floor(timeDifference / 1000);

      // Default pickup time is 15 minutes (900 seconds)
      const defaultPickupTimeSeconds = DEFAULT_PICKUP_TIME_MINUTES * 60;
      calculatedTime = Math.max(0, defaultPickupTimeSeconds - elapsedSeconds);
      calculatedTotalTime = defaultPickupTimeSeconds;
    }

    setTimeLeft(calculatedTime);
    setTotalTime(calculatedTotalTime);
    setIsInitialized(true);
  }, [acceptedAt, initialTime, isInitialized]);

  useEffect(() => {
    if (timeLeft <= 0 || !isInitialized) return;

    const timer = setInterval(() => {
      setTimeLeft(prevTime => prevTime - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, isInitialized]);

  useEffect(() => {
    if (timeLeft === 0) {
      Alert.alert(
        'Time Expired',
        'The parcel has been returned to the marketplace.',
      );
      onReturnToMarketplace();
    }
  }, [timeLeft, onReturnToMarketplace]);

  const extendTime = () => {
    if (extensions < 3) {
      const extensionTime = 180; // 3 minutes in seconds
      setTimeLeft(prev => prev + extensionTime);
      setTotalTime(prev => prev + extensionTime);
      setExtensions(prev => prev + 1);
    } else {
      Alert.alert('Limit Reached', 'You have used all 3 time extensions.');
    }
  };

  const formatTime = (seconds: number): string => {
    const min = Math.floor(seconds / 60);
    const sec = seconds % 60;
    return `${min}:${sec < 10 ? '0' : ''}${sec}`;
  };

  const progress = timeLeft / totalTime; // Calculate progress (0 to 1)

  return (
    <View style={styles.container}>
      <View style={styles.timerContainer}>
        <Svg height={CIRCLE_SIZE} width={CIRCLE_SIZE} viewBox="0 0 100 100">
          {/* Background Circle */}
          <Circle
            cx="50"
            cy="50"
            r="40"
            stroke="lightgray"
            strokeWidth={STROKE_WIDTH}
            fill="none"
          />
          {/* Progress Circle */}
          <Circle
            cx="50"
            cy="50"
            r="40"
            stroke={COLORS.primary}
            strokeWidth={STROKE_WIDTH}
            fill="none"
            strokeDasharray={`${2 * Math.PI * 40}`}
            strokeDashoffset={`${(1 - progress) * 2 * Math.PI * 40}`}
            strokeLinecap="round"
          />
        </Svg>
        <Text style={styles.timerText}>{formatTime(timeLeft)}</Text>
      </View>

      <Text>
        Pickup within this time, else it will return to the marketplace.
      </Text>

      {showExtend && <TouchableOpacity onPress={extendTime} style={styles.button}>
        <Text style={styles.buttonText}>Extend Time by 3 min</Text>
      </TouchableOpacity>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 5,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 8,
    flexDirection: 'row',
  },
  timerContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    marginEnd: 5,
  },
  timerText: {
    position: 'absolute',
    fontSize: 15,
    fontWeight: 'bold',
    color: 'black',
  },
  button: {
    padding: 5,
    borderRadius: 5,
    alignItems: 'center',
    position: 'absolute',
    end: 5,
    bottom: 0,
  },
  buttonText: {color: 'black', fontSize: 12, textDecorationLine: 'underline'},
});

export default ParcelDeliveryTimer;
