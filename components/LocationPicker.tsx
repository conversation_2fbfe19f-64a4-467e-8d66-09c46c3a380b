import React, { useEffect, useState } from "react";
import { View, Text, TouchableOpacity, Modal, StyleSheet, ActivityIndicator, PermissionsAndroid, Platform, Alert } from "react-native";
import MapView, { Marker, Region } from "react-native-maps";
import Geolocation from "react-native-geolocation-service";
import Geocoder from "react-native-geocoding";
import { GooglePlacesAutocomplete } from "react-native-google-places-autocomplete";

// Initialize Geocoder
Geocoder.init("GOOGLE_API_KEY");

interface LocationPickerProps {
  isVisible: boolean;
  onClose: () => void;
  onLocationSelect: (location: { latitude: number; longitude: number; address: string }) => void;
}

const LocationPicker: React.FC<LocationPickerProps> = ({ isVisible, onClose, onLocationSelect }) => {
  const [location, setLocation] = useState<{ latitude: number; longitude: number; address: string } | null>(null);
  const [region, setRegion] = useState<Region | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    if (Platform.OS === "android") {
      const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION);
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        getCurrentLocation();
      } else {
        setLoading(false);
      }
    } else {
      getCurrentLocation();
    }
  };

  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        let address = "Unknown Location";

        try {
          const response = await Geocoder.from(latitude, longitude);
          if (response.results.length > 0) {
            address = response.results[0].formatted_address;
          }
        } catch (error) {
          console.log("Geocoding Error:", error);
        }

        setLocation({ latitude, longitude, address });
        setRegion({
          latitude,
          longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
        setLoading(false);
      },
      (error) => {
        console.log("Location Error:", error);
        setLoading(false);
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  };

  const handleRegionChangeComplete = async (newRegion: Region) => {
    let address = "Unknown Location";
    try {
      const response = await Geocoder.from(newRegion.latitude, newRegion.longitude);
      if (response.results.length > 0) {
        address = response.results[0].formatted_address;
      }
    } catch (error) {
      console.log("Geocoding Error:", error);
    }

    setLocation({
      latitude: newRegion.latitude,
      longitude: newRegion.longitude,
      address,
    });
  };

  const handleLocationSelect = async (data: any, details: any | null) => {
    if (!details) return;

    const { lat, lng } = details.geometry.location;
    const address = details.formatted_address;

    setLocation({
      latitude: lat,
      longitude: lng,
      address,
    });

    setRegion({
      latitude: lat,
      longitude: lng,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    });
  };

  const handleConfirmLocation = () => {
    if (location) {
      onLocationSelect(location);
      onClose();
    } else {
      Alert.alert("Please select a location first.");
    }
  };

  return (
    <Modal onRequestClose={onClose} visible={isVisible} animationType="slide" transparent={false}>
      <View style={styles.container}>
        <Text style={styles.header}>Pick a Location</Text>

        <GooglePlacesAutocomplete
          placeholder="Search for a place..."
          onPress={handleLocationSelect}
          query={{
            key: "GOOGLE_API_KEY",
            language: "en",
          }}
          fetchDetails={true}
          textInputProps={{
            placeholderTextColor: 'gray'
          }}
          styles={{
            textInputContainer: styles.searchContainer,
            textInput: styles.searchInput,
            listView: {
              maxHeight: 200, // Restrict the dropdown height
              zIndex: 10,
              overflow: 'visible',
              position: 'absolute',
              flexGrow: 0,
              flexShrink: 0,
              top: 40,
              left: 20,
              right: 20,
            },
            description: { color: 'gray', },
            container: {
              zIndex: 10,
              overflow: 'visible',
              height: 50,
              flexGrow: 0,
              flexShrink: 0,
              top: 10
            },
          }}
        />

        {loading ? (
          <ActivityIndicator size="large" color="#007AFF" style={{ flex: 1 }} />
        ) : (
          <MapView
            style={styles.map}
            initialRegion={region || undefined}
            region={region || undefined}
            onRegionChangeComplete={handleRegionChangeComplete}
          >
            {location && (
              <Marker
                coordinate={{ latitude: location.latitude, longitude: location.longitude }}
                draggable
                onDragEnd={(e) => {
                  setRegion({
                    latitude: e.nativeEvent.coordinate.latitude,
                    longitude: e.nativeEvent.coordinate.longitude,
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                  });
                }}
              />
            )}
          </MapView>
        )}

        <View style={styles.infoBox}>
          <Text style={styles.locationText}>
            {location ? location.address : "Drag the pin to choose a location"}
          </Text>
        </View>

        <TouchableOpacity style={styles.button} onPress={handleConfirmLocation}>
          <Text style={styles.buttonText}>Confirm Location</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    marginVertical: 10,
    color: 'black'
  },
  searchContainer: {
    width: "90%",
    alignSelf: "center",
    marginBottom: 10
  },
  searchInput: {
    height: 40,
    borderRadius: 8,
    backgroundColor: "#f8f8f8",
    paddingHorizontal: 10,
    color: 'black'
  },
  map: {
    flex: 1,
  },
  infoBox: {
    backgroundColor: "#f8f8f8",
    padding: 10,
    borderRadius: 8,
    margin: 10,
    alignItems: "center",
  },
  locationText: {
    fontSize: 14,
    color: "#555",
  },
  button: {
    backgroundColor: "#007AFF",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
    margin: 10,
  },
  buttonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "bold",
  },
  closeButton: {
    backgroundColor: "#FF3B30",
    padding: 10,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 20,
    marginHorizontal: 10,
  },
  closeButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "bold",
  },
});

export default LocationPicker;
