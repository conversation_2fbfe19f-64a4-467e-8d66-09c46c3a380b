import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Alert} from 'react-native';
import Svg, {Circle} from 'react-native-svg';
import {COLORS} from '../constants';

interface ParcelCancelTimerProps {
  acceptedAt: string;
  onCancel: () => void;
  onTimeExpired: () => void;
}

const CIRCLE_SIZE = 65;
const STROKE_WIDTH = 6;
const CANCEL_WINDOW_MINUTES = 10; // 10 minutes window to cancel

const ParcelCancelTimer: React.FC<ParcelCancelTimerProps> = ({
  acceptedAt,
  onCancel,
  onTimeExpired,
}) => {
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isVisible, setIsVisible] = useState<boolean>(false);

  // Calculate time difference and check if within cancel window
  useEffect(() => {
    const calculateTimeLeft = () => {
      // Convert UTC accepted_at to local time
      const acceptedTime = new Date(acceptedAt);
      const currentTime = new Date();
      
      // Calculate difference in milliseconds
      const timeDifference = currentTime.getTime() - acceptedTime.getTime();
      const minutesDifference = Math.floor(timeDifference / (1000 * 60));
      
      // Check if within 10 minute window
      if (minutesDifference < CANCEL_WINDOW_MINUTES) {
        const remainingSeconds = (CANCEL_WINDOW_MINUTES * 60) - Math.floor(timeDifference / 1000);
        setTimeLeft(Math.max(0, remainingSeconds));
        setIsVisible(true);
        return remainingSeconds;
      } else {
        setIsVisible(false);
        return 0;
      }
    };

    // Initial calculation
    const initialTimeLeft = calculateTimeLeft();
    
    if (initialTimeLeft > 0) {
      // Set up timer to update every second
      const timer = setInterval(() => {
        setTimeLeft(prevTime => {
          const newTime = prevTime - 1;
          if (newTime <= 0) {
            setIsVisible(false);
            onTimeExpired();
            return 0;
          }
          return newTime;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [acceptedAt, onTimeExpired]);

  const handleCancel = () => {
    Alert.alert(
      'Cancel Parcel',
      'Are you sure you want to cancel this parcel? This action cannot be undone.',
      [
        {
          text: 'No',
          style: 'cancel',
        },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: onCancel,
        },
      ],
    );
  };

  const formatTime = (seconds: number): string => {
    const min = Math.floor(seconds / 60);
    const sec = seconds % 60;
    return `${min}:${sec < 10 ? '0' : ''}${sec}`;
  };

  const progress = timeLeft / (CANCEL_WINDOW_MINUTES * 60); // Calculate progress (0 to 1)

  if (!isVisible || timeLeft <= 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.timerContainer}>
        <Svg height={CIRCLE_SIZE} width={CIRCLE_SIZE} viewBox="0 0 100 100">
          {/* Background Circle */}
          <Circle
            cx="50"
            cy="50"
            r="40"
            stroke="lightgray"
            strokeWidth={STROKE_WIDTH}
            fill="none"
          />
          {/* Progress Circle */}
          <Circle
            cx="50"
            cy="50"
            r="40"
            stroke={COLORS.red}
            strokeWidth={STROKE_WIDTH}
            fill="none"
            strokeDasharray={`${2 * Math.PI * 40}`}
            strokeDashoffset={`${(1 - progress) * 2 * Math.PI * 40}`}
            strokeLinecap="round"
          />
        </Svg>
        <Text style={styles.timerText}>{formatTime(timeLeft)}</Text>
      </View>

      <View style={styles.textContainer}>
        <Text style={styles.mainText}>Cancel window expires in:</Text>
        <Text style={styles.subText}>
          You can cancel this parcel within 10 minutes of acceptance.
        </Text>
      </View>

      <TouchableOpacity onPress={handleCancel} style={styles.cancelButton}>
        <Text style={styles.cancelButtonText}>Cancel</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 8,
    borderWidth: 1,
    borderColor: COLORS.red,
    borderRadius: 8,
    flexDirection: 'row',
    backgroundColor: '#FFF5F5',
    marginVertical: 10,
  },
  timerContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  timerText: {
    position: 'absolute',
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.red,
  },
  textContainer: {
    flex: 1,
    marginRight: 10,
  },
  mainText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.red,
    marginBottom: 2,
  },
  subText: {
    fontSize: 12,
    color: COLORS.gray,
  },
  cancelButton: {
    backgroundColor: COLORS.red,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default ParcelCancelTimer;
