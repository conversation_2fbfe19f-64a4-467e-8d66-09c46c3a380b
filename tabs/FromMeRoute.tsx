import React, {useCallback} from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import {COLORS} from '../constants';
import {
  NavigationProp,
  useFocusEffect,
  useNavigation,
} from '@react-navigation/native';
import {useTheme} from '../theme/ThemeProvider';
import moment from 'moment';
import {useAppDispatch, useAppSelector} from '../redux/hooks';
import {getParcels} from '../redux/parcels/parcelsSlice';
import {myOrders} from '../data';

const FromMeRoute = () => {
  const navigation = useNavigation<NavigationProp<any>>();
  const token = useAppSelector(state => state.auth.userData?.token);
  const parcelList = useAppSelector(state => state.parcels.myParcels);
  const dispatch = useAppDispatch();
  const {dark} = useTheme();

  useFocusEffect(
    useCallback(() => {
      if (token) {
        dispatch(getParcels(token));
      }
      return () => {};
    }, [token]),
  );

  return (
    <View style={styles.container}>
      <FlatList
        // dynamic data
        data={parcelList}
        // static data
        // data={myOrders}
        contentContainerStyle={{paddingBottom: 70}}
        showsVerticalScrollIndicator={false}
        keyExtractor={item => item.id.toString()}
        renderItem={({item, index}) => (
          <View style={styles.itemContainer}>
            <View style={styles.statusContainer}>
              <Text
                style={[
                  styles.typeText,
                  {
                    color: dark ? COLORS.white : COLORS.black,
                  },
                ]}>
                {item.category.name}
              </Text>
              <Text
                style={[
                  styles.statusText,
                  {
                    color:
                      item.status === 'pending'
                        ? 'red'
                        : item.status === 'accepted'
                        ? '#ebab34'
                        : 'green',
                    marginLeft: 12,
                  },
                ]}>
                {item.status}
              </Text>
            </View>
            <View style={styles.infoContainer}>
              <View style={styles.infoLeft}>
                <Image
                  source={{uri: item.media[0].mediaUrl}}
                  style={styles.itemImage}
                />
                <View style={styles.itemDetails}>
                  <Text
                    style={[
                      styles.itemName,
                      {
                        color: dark ? COLORS.white : COLORS.black,
                      },
                    ]}
                    numberOfLines={2}>
                    {item.title || item.description}
                  </Text>
                  <View style={styles.itemSubDetails}>
                    <Text style={styles.itemPrice}>AED {item.amount}</Text>
                    <Text
                      style={[
                        styles.itemDate,
                        {
                          color: dark
                            ? COLORS.grayscale400
                            : COLORS.grayscale700,
                        },
                      ]}>
                      {' '}
                      | {moment(item.createdAt).format('DD MMM YYYY, HH:mm a')}
                    </Text>
                    <Text
                      style={[
                        styles.itemItems,
                        {
                          color: dark
                            ? COLORS.grayscale400
                            : COLORS.grayscale700,
                        },
                      ]}>
                      {' '}
                      | Weight: {item.weight} kg
                    </Text>
                  </View>
                </View>
              </View>
              <Text
                style={[
                  styles.receiptText,
                  {
                    color: dark ? COLORS.white : COLORS.grayscale700,
                  },
                ]}>
                {item.id}
              </Text>
            </View>
            <View style={styles.actionsContainer}>
              <TouchableOpacity
                onPress={() => navigation.navigate('parceldetails', {parcel: item, myOrder: true})}
                style={styles.rateButton}>
                <Text style={styles.rateButtonText}>View</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => navigation.navigate('myordertrack')}
                style={styles.reorderButton}>
                <Text style={styles.reorderButtonText}>Track Order</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  itemContainer: {
    flexDirection: 'column',
  },
  statusContainer: {
    borderBottomColor: COLORS.grayscale400,
    borderBottomWidth: 0.4,
    marginVertical: 12,
    flexDirection: 'row',
    paddingBottom: 4,
  },
  typeText: {
    fontSize: 14,
    fontFamily: 'Urbanist Bold',
  },
  statusText: {
    fontSize: 14,
    fontFamily: 'Urbanist Bold',
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  infoLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemImage: {
    height: 60,
    width: 60,
    borderRadius: 8,
  },
  itemDetails: {
    marginLeft: 12,
  },
  itemName: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  itemSubDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  itemPrice: {
    fontSize: 14,
    fontFamily: 'Urbanist Bold',
    color: COLORS.primary,
  },
  itemDate: {
    fontSize: 12,
    fontFamily: 'Urbanist Regular',
    marginHorizontal: 2,
  },
  itemItems: {
    fontSize: 12,
    fontFamily: 'Urbanist Regular',
  },
  receiptText: {
    fontSize: 14,
    textDecorationLine: 'underline',
    textDecorationColor: COLORS.gray,
    fontFamily: 'Urbanist Regular',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 18,
  },
  rateButton: {
    height: 38,
    width: 140,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: COLORS.primary,
    borderWidth: 1,
    borderRadius: 8,
  },
  rateButtonText: {
    color: COLORS.primary,
    fontSize: 14,
    fontFamily: 'Urbanist Regular',
  },
  reorderButton: {
    height: 38,
    width: 140,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: 8,
  },
  reorderButtonText: {
    color: COLORS.white,
    fontSize: 14,
    fontFamily: 'Urbanist Regular',
  },
});

export default FromMeRoute;
