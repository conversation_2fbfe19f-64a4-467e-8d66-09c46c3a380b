import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import MapView, {Marker} from 'react-native-maps';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  NavigationProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import Button from '../components/Button';
import {COLORS, icons} from '../constants';
import {useTheme} from '../theme/ThemeProvider';
import Feather from 'react-native-vector-icons/Feather';
import ParcelDeliveryTimer from '../components/ParcelDeliveryTimer';
import {useAppDispatch, useAppSelector} from '../redux/hooks';
import {deliverParcel, getParcelById, pickedParcel} from '../redux/parcels/parcelsSlice';

const ParcelLive = () => {
  const navigation = useNavigation<NavigationProp<any>>();
  const route = useRoute<any>();
  const {dark, colors} = useTheme();
  const mapRef = useRef<MapView>(null);
  const {myOrder} = route.params;
  const parcelA = route.params.parcel;
  const [parcel, setParcel] = useState(parcelA);
  const [status, setStatus] = useState(parcel.status);
  const [otpModalVisible, setOtpModalVisible] = useState(false);
  const token = useAppSelector(state => state.auth.userData?.token);
  const [otp, setOtp] = useState('');
  const dispatch = useAppDispatch();

  useFocusEffect(
    useCallback(() => {
      if (parcelA && token) {
        dispatch(getParcelById({ token: token, id: parcel.id }))
          .unwrap()
          .then((response: any) => {
            if (response.parcel && response.parcel.id) {
              console.log(response.parcel.id, 'response.parcel.id');
              setStatus(response.parcel.status);
              setParcel(response.parcel);
            }
          })
          .catch((error: any) => {
            console.error('Error fetching parcel details:', error);
          });
      }
  
      // optional cleanup
      return () => {
        // cleanup logic if needed
      };
    }, [parcelA, token])
  );

  const updateParcel = () => {
    dispatch(getParcelById({token: token, id: parcel.id}))
      .unwrap()
      .then((response: any) => {
        if (response.parcel && response.parcel.id) {
          setStatus(response.parcel.status);
          setParcel(response.parcel);
        }
      })
      .catch((error: any) => {
        console.error('Error fetching parcel details:', error);
      });
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={[
          styles.headerIconContainer,
          {borderColor: dark ? 'transparent' : COLORS.grayscale200},
        ]}>
        <Feather
          name="arrow-left"
          size={24}
          color={dark ? COLORS.white : COLORS.black}
        />
      </TouchableOpacity>
      <Text
        style={[
          styles.headerTitle,
          {color: dark ? COLORS.white : COLORS.black},
        ]}>
        Parcel Status
      </Text>
      <View style={styles.headerIconPlaceholder} />
    </View>
  );

  const onSubmitClick = () => {
    if (status == 'accepted') {
      dispatch(pickedParcel({token: token, id: parcel.id, otp: otp}))
        .unwrap()
        .then((response: any) => {
          if (response.status) {
            updateParcel();
            Alert.alert('Parcel Picked', response.message, [
              {
                text: 'OK',
                onPress: () => {},
              },
            ]);
          } else {
            Alert.alert('Error', response.message, [
              {
                text: 'OK',
                onPress: () => {},
              },
            ]);
          }
        })
        .catch((error: any) => {
          Alert.alert('Error', error.message || 'Something went wrong.', [
            {
              text: 'OK',
              onPress: () => {},
            },
          ]);
        });
    } else if (status == 'picked') {
      dispatch(deliverParcel({token: token, id: parcel.id, otp: otp}))
        .unwrap()
        .then((response: any) => {
          if (response.status) {
            updateParcel();
            Alert.alert('Parcel Delivered', response.message, [
              {
                text: 'OK',
                onPress: () => {
                  navigation.goBack();
                },
              },
            ]);
          } else {
            Alert.alert('Error', response.message, [
              {
                text: 'OK',
                onPress: () => {},
              },
            ]);
          }
        })
        .catch((error: any) => {
          Alert.alert('Error', error.message || 'Something went wrong.', [
            {
              text: 'OK',
              onPress: () => {},
            },
          ]);
        });
    }
    setOtp('');
    setOtpModalVisible(false);
  };

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: colors.background}]}>
      {renderHeader()}
      <MapView
        ref={mapRef}
        style={styles.map}
        initialRegion={{
          latitude: parseFloat(parcel.pickup_lat),
          longitude: parseFloat(parcel.pickup_long),
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        }}>
        <Marker
          coordinate={{
            latitude: parseFloat(parcel.pickup_lat),
            longitude: parseFloat(parcel.pickup_long),
          }}
          title="Pickup Location"
          description={parcel.pickup_address}
        />
      </MapView>
      <View style={styles.buttonContainer}>
        <Text style={styles.statusText}>
          {status == 'pending'
            ? 'Parcel is waiting to accept by someone'
            : status == 'accepted'
            ? 'Way to pickup parcel'
            : status == 'picked'
            ? 'Way to deliver parcel'
            : 'Parcel is delivered to delivered user.'}
        </Text>
        {status == 'accepted' && (
          <View style={{marginBottom: 10}}>
            <ParcelDeliveryTimer
              acceptedAt={parcel.acceptance?.accepted_at}
              onReturnToMarketplace={() => {}}
              showExtend={!myOrder}
            />
          </View>
        )}
        {(status == 'accepted' || status == 'picked') && !myOrder && (
          <Button
            title={
              status == 'accepted' ? 'Ready to Pickup' : 'Ready to Deliver'
            }
            filled
            onPress={() => setOtpModalVisible(true)}
          />
        )}
      </View>
      <Modal
        transparent={true}
        visible={otpModalVisible}
        animationType="fade"
        onRequestClose={() => setOtpModalVisible(false)}>
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {status == 'accepted' ? (
              <Text style={styles.modalTitle}>
                Enter sender's OTP to verify pickup.
              </Text>
            ) : (
              <Text style={styles.modalTitle}>
                Enter receiver's OTP to verify pickup.
              </Text>
            )}
            <TextInput
              style={styles.otpInput}
              keyboardType="numeric"
              placeholder="Enter OTP"
              value={otp}
              onChangeText={setOtp}
            />
            <View style={styles.modalButtons}>
              <Button
                style={{width: '45%', height: 45}}
                title="Cancel"
                onPress={() => setOtpModalVisible(false)}
              />
              <Button
                style={{width: '45%', height: 45}}
                title="Confirm"
                filled
                onPress={onSubmitClick}
              />
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  statusText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 15,
  },
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerIconContainer: {
    height: 46,
    width: 46,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 999,
  },
  headerIconPlaceholder: {
    width: 46,
  },
  headerTitle: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
  },
  map: {
    flex: 1,
  },
  buttonContainer: {
    padding: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  otpInput: {
    width: '100%',
    height: 40,
    borderWidth: 1,
    borderColor: COLORS.grayscale200,
    borderRadius: 5,
    paddingHorizontal: 10,
    marginBottom: 10,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 10,
  },
});

export default ParcelLive;
