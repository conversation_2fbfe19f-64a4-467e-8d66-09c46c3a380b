import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
  ImageSourcePropType,
  FlatList,
  Switch,
  TextInput,
  Dimensions,
} from 'react-native';
import React, {useCallback, useEffect, useReducer, useState} from 'react';
import {COLORS, SIZES, icons} from '../constants';
import {SafeAreaView} from 'react-native-safe-area-context';
import Feather from 'react-native-vector-icons/Feather';
import {validateInput} from '../utils/actions/formActions';
import Input from '../components/Input';
import {reducer} from '../utils/reducers/formReducers';
import Button from '../components/Button';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {useTheme} from '../theme/ThemeProvider';
import InputLabel from '../components/InputLabel';
import LocationPicker from '../components/LocationPicker';
import {Asset, launchImageLibrary} from 'react-native-image-picker';
import {useAppDispatch, useAppSelector} from '../redux/hooks';
import {
  createParcel,
  getCategory,
  updateParcel,
} from '../redux/parcels/parcelsSlice';
import {Dropdown} from 'react-native-element-dropdown';

const packageOptions = [
  {
    label: 'Envelope',
    icon: icons.envelop,
    value: 'envelope',
    weight: ['UPTO 0.5 KG', '0.5 KG-2 KG'],
  },
  {
    label: 'Box',
    icon: icons.box_parcel,
    value: 'box',
    weight: ['0.5 KG-2 KG', '2 KG - 5 KG', '5 KG - 10 KG'],
  },
  {
    label: 'Suitcase',
    icon: icons.luggage,
    value: 'suitcase',
    weight: ['UPTO 5 KG', '5 KG - 10 KG', '10 KG - 20 KG'],
  },
  {
    label: 'Backpack',
    icon: icons.backpack,
    value: 'backpack',
    weight: ['0.5 KG-2 KG', '2 KG - 5 KG', '5 KG - 10 KG'],
  },
  {
    label: 'Other',
    icon: icons.other,
    value: 'other',
    weight: ['UPTO 5 KG', '5 KG - 10 KG', '10 KG - 20 KG'],
  },
];

const getParcelOtherPackageType = (parcel: any) => {
  if (parcel.package_type) {
    if (parcel.package_type == 'other') {
      return parcel.package_type;
    } else {
      const otherPackge = packageOptions.filter(
        item => item.value == parcel.package_type,
      );
      if (otherPackge.length > 0) {
        return '';
      } else {
        return parcel.package_type;
      }
    }
  }
  return '';
};

const OrderForm = () => {
  const {colors, dark} = useTheme();
  const route = useRoute<any>();
  const parcel = route.params?.parcel;

  const initialState = {
    inputValues: {
      fullName: parcel ? parcel.full_name : '',
      email: parcel ? parcel.email : '',
      phoneNumber: parcel ? parcel.mobile : '',
      amount: parcel ? parcel.amount : '',
      insured: parcel ? parcel.insured : false,
      description: parcel ? parcel.description : '',
      weight: parcel ? parcel.weight : '',
      length: parcel ? parcel.length : '',
      width: parcel ? parcel.width : '',
      height: parcel ? parcel.height : '',
      title: parcel ? parcel.title : '',
      otherPackageType: parcel ? getParcelOtherPackageType(parcel) : '',
    },
    inputValidities: {
      fullName: false,
      phoneNumber: false,
      amount: false,
      insured: true,
      description: false,
      weight: false,
      length: false,
      width: false,
      height: false,
      title: false,
      email: false,
      otherPackageType: false,
    },
    formIsValid: false,
  };

  const navigation = useNavigation<NavigationProp<any>>();
  const [formState, dispatchFormState] = useReducer(reducer, initialState);
  const [pickupLocation, setPickupLocation] = useState<any>(
    parcel
      ? {
          latitude: parcel.pickup_lat,
          longitude: parcel.pickup_long,
          address: parcel.pickup_address,
        }
      : null,
  );
  const [deliveryLocation, setDeliveryLocation] = useState<any>(
    parcel
      ? {
          latitude: parcel.delivery_lat,
          longitude: parcel.delivery_long,
          address: parcel.delivery_address,
        }
      : null,
  );
  const [images, setImages] = useState<Asset[]>(
    parcel
      ? parcel.media.map((item: any) => {
          return {uri: item.mediaUrl, mediaId: item.id};
        })
      : [],
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isPickupModalVisible, setIsPickupModalVisible] = useState(false);
  const [isDeliveryModalVisible, setIsDeliveryModalVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(
    parcel ? parcel.category.id : null,
  );
  const token = useAppSelector(state => state.auth.userData?.token);
  const categories = useAppSelector(state => state.parcels.categoryList);
  const [deletedMedia, setDeletedMedia] = useState<any[]>([]);
  const [selectedPackage, setSelectedPackage] = useState(
    parcel
      ? getParcelOtherPackageType(parcel)
        ? 'other'
        : parcel.package_type
      : null,
  );
  const [selectedWeight, setSelectedWeight] = useState(
    parcel ? parcel.weight : '',
  );
  const selectedWeights =
    packageOptions.find(p => p.value === selectedPackage)?.weight || [];

  const dispatch = useAppDispatch();

  useEffect(() => {
    if (token) {
      dispatch(getCategory(token));
    }
  }, [token]);

  const inputChangedHandler = useCallback(
    (inputId: string, inputValue: any) => {
      const result = validateInput(inputId, inputValue);
      dispatchFormState({
        inputId,
        validationResult: result,
        inputValue,
      });
    },
    [dispatchFormState],
  );

  useEffect(() => {
    if (error) {
      Alert.alert('An error occurred', error);
    }
  }, [error]);

  const handleSubmit = async () => {
    if (!pickupLocation || !deliveryLocation) {
      Alert.alert('Error', 'Please select both pickup and drop locations');
      return;
    }
    if (!selectedCategory) {
      Alert.alert('Error', 'Please select a category');
      return;
    }
    if (!selectedPackage) {
      Alert.alert('Error', 'Please select package type');
      return;
    }
    if (!selectedWeight) {
      Alert.alert('Error', 'Please select package weight');
      return;
    }

    const package_type =
      selectedPackage == 'other'
        ? formState.inputValues.otherPackageType
        : selectedPackage;

    const formData = new FormData();
    formData.append('full_name', formState.inputValues.fullName);
    formData.append('mobile', formState.inputValues.phoneNumber);
    formData.append('email', formState.inputValues.email);
    formData.append('categoryId', selectedCategory);
    formData.append('insured', formState.inputValues.insured);
    formData.append('amount', formState.inputValues.amount);
    formData.append('pickup_lat', pickupLocation?.latitude);
    formData.append('pickup_long', pickupLocation?.longitude);
    formData.append('pickup_address', pickupLocation?.address);
    formData.append('delivery_lat', deliveryLocation?.latitude);
    formData.append('delivery_long', deliveryLocation?.longitude);
    formData.append('delivery_address', deliveryLocation?.address);
    formData.append('description', formState.inputValues.description);
    formData.append('weight', selectedWeight);
    formData.append('package_type', package_type);
    // formData.append('weight', formState.inputValues.weight);
    formData.append('length', formState.inputValues.length);
    formData.append('width', formState.inputValues.width);
    formData.append('height', formState.inputValues.height);
    formData.append('title', formState.inputValues.title);
    if (parcel) {
      formData.append('deletedMedia', JSON.stringify(deletedMedia));
    }

    const mediaImage = images.filter((img: any) => !img.mediaId);
    mediaImage.forEach((img: any, index) => {
      formData.append('file', {
        uri: img.uri,
        name: `image_${index}.jpg`,
        type: img.type,
      });
    });

    setIsLoading(true);
    const body = {
      token: token,
      data: formData,
      id: parcel ? parcel.id : null,
    };
    if (parcel) {
      dispatch(updateParcel(body))
        .unwrap()
        .then((data: any) => {
          if (data.status) {
            Alert.alert('Success', 'Parcel updated successfully!');
            navigation.goBack();
          } else {
            Alert.alert('Error', data?.message || 'Something went wrong');
          }
          setIsLoading(false);
        })
        .catch((error: any) => {
          Alert.alert('Error', error?.message || 'Something went wrong');
          setIsLoading(false);
        });
    } else {
      dispatch(createParcel(body))
        .unwrap()
        .then((data: any) => {
          if (data.status) {
            Alert.alert('Success', 'Parcel created successfully!');
            navigation.goBack();
          } else {
            Alert.alert('Error', data?.message || 'Something went wrong');
          }
          setIsLoading(false);
        })
        .catch((error: any) => {
          Alert.alert('Error', error?.message || 'Something went wrong');
          setIsLoading(false);
        });
    }
  };

  const selectImages = () => {
    launchImageLibrary({mediaType: 'photo', selectionLimit: 5}, response => {
      if (response.assets) setImages([...images, ...response.assets]);
    });
  };

  const removeImage = (index: number, item: any) => {
    if (item.mediaId) {
      setDeletedMedia(prev => [...prev, item.mediaId]);
    }
    setImages(prevImages => prevImages.filter((_, i) => i !== index));
  };

  return (
    <SafeAreaView style={[styles.area, {backgroundColor: colors.background}]}>
      <View style={[styles.container, {backgroundColor: colors.background}]}>
        <View style={styles.headerContainer}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={[
              styles.headerIconContainer,
              {borderColor: dark ? COLORS.grayscale400 : COLORS.grayscale200},
            ]}>
            <Image
              source={icons.arrowLeft as ImageSourcePropType}
              resizeMode="contain"
              style={[
                styles.arrowBackIcon,
                {tintColor: dark ? COLORS.white : COLORS.black},
              ]}
            />
          </TouchableOpacity>
          <Text
            style={[
              styles.headerTitle,
              {color: dark ? COLORS.white : COLORS.black},
            ]}>
            Parcel Details
          </Text>
          <TouchableOpacity>
            <Feather
              name="more-vertical"
              size={24}
              color={dark ? COLORS.white : 'black'}
            />
          </TouchableOpacity>
        </View>
        <ScrollView showsVerticalScrollIndicator={false}>
          <InputLabel title="Full Name" />
          <Input
            id="fullName"
            onInputChanged={inputChangedHandler}
            errorText={formState.inputValidities['fullName']}
            value={formState.inputValues.fullName}
            placeholder="Receiver's Full Name"
          />
          <InputLabel title="Phone Number" />
          <Input
            id="phoneNumber"
            onInputChanged={inputChangedHandler}
            value={formState.inputValues.phoneNumber}
            errorText={formState.inputValidities['phoneNumber']}
            placeholder="Receiver's Phone Number"
          />
          <InputLabel title="Email" />
          <Input
            id="email"
            onInputChanged={inputChangedHandler}
            value={formState.inputValues.email}
            errorText={formState.inputValidities['email']}
            placeholder="Receiver's Email"
          />
          <InputLabel title="Category" />
          <Dropdown
            style={{
              height: 50,
              borderColor: COLORS.grayscale200,
              borderWidth: 1,
              borderRadius: 8,
              paddingHorizontal: 10,
            }}
            itemTextStyle={{
              color: 'black',
            }}
            data={categories.map(cat => ({label: cat.name, value: cat.id}))}
            labelField="label"
            valueField="value"
            placeholder="Select Category"
            value={selectedCategory}
            onChange={item => setSelectedCategory(item.value)}
          />
          <InputLabel title="Pickup Location" />
          <TouchableOpacity onPress={() => setIsPickupModalVisible(true)}>
            <Text
              style={{
                textDecorationLine: pickupLocation ? undefined : 'underline',
                color: pickupLocation ? undefined : COLORS.primary,
              }}>
              {pickupLocation
                ? pickupLocation.address
                : 'Select Pickup Location'}
            </Text>
          </TouchableOpacity>
          <InputLabel title="Drop-off Location" />
          <TouchableOpacity onPress={() => setIsDeliveryModalVisible(true)}>
            <Text
              style={{
                textDecorationLine: deliveryLocation ? undefined : 'underline',
                color: deliveryLocation ? undefined : COLORS.primary,
              }}>
              {deliveryLocation
                ? deliveryLocation.address
                : 'Select Drop-off Location'}
            </Text>
          </TouchableOpacity>
          <InputLabel title="Images" />
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <TouchableOpacity onPress={selectImages} style={styles.imagePicker}>
              <Feather name="plus" size={24} color={COLORS.black} />
            </TouchableOpacity>
            <FlatList
              data={images}
              horizontal
              keyExtractor={(item, index) => index.toString()}
              renderItem={({item, index}) => (
                <View style={styles.imageContainer}>
                  <Image source={{uri: item.uri}} style={styles.image} />
                  <TouchableOpacity
                    style={styles.removeImage}
                    onPress={() => removeImage(index, item)}>
                    <Feather name="x" size={16} color={COLORS.white} />
                  </TouchableOpacity>
                </View>
              )}
            />
          </View>
          <InputLabel title="Insured" />
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 10,
            }}>
            <Switch
              value={formState.inputValues.insured}
              onValueChange={newValue => {
                inputChangedHandler('insured', newValue);
              }}
            />
            <Text style={{marginLeft: 10}}>
              {formState.inputValues.insured ? 'Yes' : 'No'}
            </Text>
          </View>
          <InputLabel title="Package Type" />
          <View style={styles.packageRow}>
            {packageOptions.map(option => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.packageOption,
                  selectedPackage === option.value &&
                    styles.packageOptionSelected,
                ]}
                onPress={() => {
                  setSelectedWeight('');
                  setSelectedPackage(option.value);
                }}>
                <Image
                  source={option.icon as ImageSourcePropType}
                  style={styles.packageIcon}
                  resizeMode="contain"
                />
                <Text style={styles.packageLabel}>{option.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
          {selectedPackage == 'other' && (
            <Input
              id="otherPackageType"
              onInputChanged={inputChangedHandler}
              value={formState.inputValues.otherPackageType}
              errorText={formState.inputValidities['otherPackageType']}
              placeholder="Other (Please Specify)"
            />
          )}
          {selectedWeights.length > 0 && (
            <>
              <InputLabel title="Select Weight" />
              <View style={styles.weightRow}>
                {selectedWeights.map(weight => (
                  <TouchableOpacity
                    key={weight}
                    style={[
                      styles.weightOption,
                      selectedWeight === weight && styles.weightOptionSelected,
                    ]}
                    onPress={() => setSelectedWeight(weight)}>
                    <Text
                      style={[
                        styles.weightLabel,
                        selectedWeight === weight && styles.weightLabelSelected,
                      ]}>
                      {weight}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </>
          )}

          <View style={styles.inputContainer}>
            <View
              style={[
                styles.inputLeft,
                !(selectedWeight && selectedWeight.includes('+ KG')) && {
                  width: SIZES.width - 32,
                },
              ]}>
              <InputLabel title="Package Title" />
              <View
                style={[
                  styles.fullNameInput,
                  {
                    backgroundColor: dark
                      ? COLORS.dark2
                      : COLORS.secondaryWhite,
                  },
                  !(selectedWeight && selectedWeight.includes('+ KG')) && {
                    width: '100%',
                  },
                ]}>
                <Image
                  source={icons.box2}
                  resizeMode="contain"
                  style={styles.boxIcon}
                />
                <TextInput
                  placeholder="Enter package title"
                  value={formState.inputValues.title}
                  placeholderTextColor={COLORS.gray}
                  onChangeText={text => inputChangedHandler('title', text)}
                  style={styles.packageTypeInput}
                />
              </View>
            </View>
            {selectedWeight && selectedWeight.includes('+ KG') && (
              <View style={styles.inputRight}>
                <InputLabel title="Weight" />
                <View
                  style={[
                    styles.weightInput,
                    {
                      backgroundColor: dark
                        ? COLORS.dark2
                        : COLORS.secondaryWhite,
                    },
                  ]}>
                  <TextInput
                    placeholder="0"
                    placeholderTextColor={COLORS.gray}
                    value={formState.inputValues.weight}
                    onChangeText={text => inputChangedHandler('weight', text)}
                    style={[
                      styles.weightInputName,
                      {
                        backgroundColor: dark
                          ? COLORS.dark2
                          : COLORS.secondaryWhite,
                      },
                    ]}
                  />
                  <Text style={styles.weightText}>Kg</Text>
                </View>
              </View>
            )}
          </View>
          {selectedWeight && selectedWeight.includes('+ KG') && (
            <InputLabel title="Dimensions" />
          )}
          {selectedWeight && selectedWeight.includes('+ KG') && (
            <View style={styles.dimensionContainer}>
              <View
                style={[
                  styles.dimensionInputContainer,
                  {
                    backgroundColor: dark
                      ? COLORS.dark2
                      : COLORS.secondaryWhite,
                  },
                ]}>
                <TextInput
                  placeholder="Length"
                  placeholderTextColor={COLORS.gray}
                  value={formState.inputValues.length}
                  onChangeText={text => inputChangedHandler('length', text)}
                  style={styles.dimensionInput}
                  keyboardType="numeric"
                />
                <Text
                  style={[
                    styles.dimensionText,
                    {
                      color: dark ? COLORS.gray : COLORS.black,
                    },
                  ]}>
                  Cm
                </Text>
              </View>
              <View
                style={[
                  styles.dimensionInputContainer,
                  {
                    backgroundColor: dark
                      ? COLORS.dark2
                      : COLORS.secondaryWhite,
                  },
                ]}>
                <TextInput
                  placeholder="Width"
                  placeholderTextColor={COLORS.gray}
                  value={formState.inputValues.width}
                  onChangeText={text => inputChangedHandler('width', text)}
                  style={styles.dimensionInput}
                  keyboardType="numeric"
                />
                <Text
                  style={[
                    styles.dimensionText,
                    {
                      color: dark ? COLORS.gray : COLORS.black,
                    },
                  ]}>
                  Cm
                </Text>
              </View>
              <View
                style={[
                  styles.dimensionInputContainer,
                  {
                    backgroundColor: dark
                      ? COLORS.dark2
                      : COLORS.secondaryWhite,
                  },
                ]}>
                <TextInput
                  placeholder="Height"
                  placeholderTextColor={COLORS.gray}
                  value={formState.inputValues.height}
                  style={styles.dimensionInput}
                  onChangeText={text => inputChangedHandler('height', text)}
                  keyboardType="numeric"
                />
                <Text
                  style={[
                    styles.dimensionText,
                    {
                      color: dark ? COLORS.gray : COLORS.black,
                    },
                  ]}>
                  Cm
                </Text>
              </View>
            </View>
          )}
          <InputLabel title="Package Description" />
          <Input
            id="description"
            onInputChanged={inputChangedHandler}
            value={formState.inputValues.description}
            errorText={formState.inputValidities['description']}
            placeholder="Enter Package Description"
          />
          <InputLabel title="Amount Offered" />
          <Input
            id="amount"
            onInputChanged={inputChangedHandler}
            value={formState.inputValues.amount}
            errorText={formState.inputValidities['amount']}
            placeholder="Enter Amount Offered"
          />
          <Button
            title={parcel ? 'Update' : 'Submit'}
            filled
            style={styles.continueBtn}
            onPress={handleSubmit}
            isLoading={isLoading}
          />
        </ScrollView>
      </View>
      <LocationPicker
        isVisible={isPickupModalVisible}
        onClose={() => setIsPickupModalVisible(false)}
        onLocationSelect={setPickupLocation}
      />
      <LocationPicker
        isVisible={isDeliveryModalVisible}
        onClose={() => setIsDeliveryModalVisible(false)}
        onLocationSelect={setDeliveryLocation}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  weightRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  weightOption: {
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.gray,
    backgroundColor: COLORS.white,
    marginRight: 8,
    marginBottom: 8,
  },
  weightOptionSelected: {
    borderColor: COLORS.primary,
    backgroundColor: '#a6e3d8',
  },
  weightLabel: {
    color: COLORS.black,
    fontSize: 14,
  },
  weightLabelSelected: {
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  packageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  packageOption: {
    alignItems: 'center',
    padding: 10,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.gray,
    width: (SIZES.width - 45) / 5, // space between 4 items
  },
  packageOptionSelected: {
    backgroundColor: '#a6e3d8',
    borderColor: COLORS.primary,
  },
  packageIcon: {
    width: 30,
    height: 30,
    marginBottom: 5,
  },
  packageLabel: {
    fontSize: 10,
    textAlign: 'center',
  },
  dimensionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: SIZES.width - 32,
  },
  dimensionInputContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.secondaryWhite,
    width: (SIZES.width - 44) / 3,
    height: 48,
    borderRadius: 12,
    alignItems: 'center',
    paddingRight: 6,
  },
  dimensionInput: {
    flex: 1,
    paddingHorizontal: 4,
    fontSize: 12,
    color: COLORS.black,
    fontFamily: 'Urbanist Regular',
  },
  dimensionText: {
    fontSize: 14,
    fontFamily: 'Urbanist Medium',
    color: COLORS.black,
  },
  inputRight: {
    flexDirection: 'column',
  },
  weightText: {
    fontSize: 14,
    fontFamily: 'Urbanist Medium',
    color: COLORS.gray,
    textAlign: 'center',
  },
  packageTypeInput: {
    paddingHorizontal: 12,
  },
  weightInput: {
    height: 48,
    backgroundColor: COLORS.secondaryWhite,
    width: SIZES.width - ((SIZES.width - 32) * 3) / 5 - 45,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    overflow: 'hidden',
  },
  weightInputName: {
    backgroundColor: COLORS.secondaryWhite,
    paddingHorizontal: 12,
  },
  fullNameInput: {
    height: 48,
    backgroundColor: COLORS.secondaryWhite,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    width: ((SIZES.width - 32) * 3) / 5,
    borderRadius: 12,
    marginRight: 12,
  },
  boxIcon: {
    width: 24,
    height: 24,
    tintColor: COLORS.gray,
  },
  inputLeft: {
    flexDirection: 'column',
  },
  inputContainer: {
    flexDirection: 'row',
    width: SIZES.width - 32,
    flex: 1,
  },
  dropdownContainer: {
    borderWidth: 1,
    borderColor: COLORS.greyscale600,
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginBottom: 10,
  },
  imagePicker: {
    width: 80,
    height: 80,
    backgroundColor: COLORS.grayscale200,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    marginRight: 10,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 10,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 10,
  },
  removeImage: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
    padding: 4,
  },
  area: {flex: 1, backgroundColor: COLORS.white},
  container: {flex: 1, backgroundColor: COLORS.white, padding: 16},
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerIconContainer: {
    height: 46,
    width: 46,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 999,
  },
  arrowBackIcon: {width: 24, height: 24, tintColor: COLORS.black},
  headerTitle: {fontSize: 16, fontFamily: 'Urbanist Bold', color: COLORS.black},
  continueBtn: {borderRadius: 30, marginVertical: 22, width: SIZES.width - 32},
});

export default OrderForm;
