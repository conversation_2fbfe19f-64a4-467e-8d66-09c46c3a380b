import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  Platform,
  ImageSourcePropType,
} from 'react-native';
import React, {useCallback, useEffect, useReducer, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {COLORS, SIZES, icons, images} from '../constants';
import Header from '../components/Header';
import {reducer} from '../utils/reducers/formReducers';
import {validateInput} from '../utils/actions/formActions';
import CheckBox from '@react-native-community/checkbox';
import Button from '../components/Button';
import {useTheme} from '../theme/ThemeProvider';
import {Image} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import Input from '../components/Input';
import {useAppDispatch} from '../redux/hooks';
import {forgetPassword} from '../redux/auth/authSlice';

type Nav = {
  navigate: (value: string, payload?: any) => void;
};

const isTestMode = false;

const initialState = {
  inputValues: {
    email: isTestMode ? '<EMAIL>' : '',
  },
  inputValidities: {
    email: false,
  },
  formIsValid: false,
};

const ForgotPasswordEmail = () => {
  const {navigate} = useNavigation<Nav>();
  const [formState, dispatchFormState] = useReducer(reducer, initialState);
  const [error, setError] = useState(null);
  const [isChecked, setChecked] = useState(false);
  const {colors, dark} = useTheme();
  const dispatch = useAppDispatch();

  const inputChangedHandler = useCallback(
    (inputId: string, inputValue: string) => {
      const result = validateInput(inputId, inputValue);
      dispatchFormState({
        inputId,
        validationResult: result,
        inputValue,
      });
    },
    [dispatchFormState],
  );

  useEffect(() => {
    if (error) {
      Alert.alert('An error occured', error);
    }
  }, [error]);

  const onResetPassword = () => {
    if (formState.formIsValid) {
      const body = formState.inputValues;
      dispatch(forgetPassword(body.email))
        .unwrap()
        .then((data: {status: boolean; data: string; message: string}) => {
          if (data.status) {
            navigate('otpverification', {email: body.email});
          } else {
            Alert.alert('Error', data?.message || 'Something went wrong');
          }
        })
        .catch((error: any) => {
          Alert.alert('Error', error?.message || 'Something went wrong');
        });
    } else {
      inputChangedHandler('email', formState.inputValues.email);
    }
  };

  return (
    <SafeAreaView style={[styles.area, {backgroundColor: colors.background}]}>
      <View style={[styles.container, {backgroundColor: colors.background}]}>
        <Header title="Forgot Password" />
        <ScrollView
          style={{marginVertical: 54}}
          showsVerticalScrollIndicator={false}>
          <View style={styles.logoContainer}>
            <Image
              source={images.logo as ImageSourcePropType}
              resizeMode="contain"
              style={styles.logo}
            />
          </View>
          <Text
            style={[
              styles.title,
              {
                color: dark ? COLORS.white : COLORS.black,
              },
            ]}>
            Enter to Your Email
          </Text>
          <Input
            id="email"
            onInputChanged={inputChangedHandler}
            errorText={formState.inputValidities['email']}
            placeholder="Email"
            placeholderTextColor={dark ? COLORS.grayTie : COLORS.black}
            icon={icons.email}
            keyboardType="email-address"
          />
          <View style={styles.checkBoxContainer}>
            <View style={{flexDirection: 'row'}}>
              <CheckBox
                style={styles.checkbox}
                value={isChecked}
                boxType="square"
                onTintColor={isChecked ? COLORS.primary : 'gray'}
                onFillColor={isChecked ? COLORS.primary : 'gray'}
                onCheckColor={COLORS.white}
                onValueChange={setChecked}
                tintColors={{true: COLORS.primary, false: 'gray'}}
              />
              <View style={{flex: 1}}>
                <Text
                  style={[
                    styles.privacy,
                    {
                      color: dark ? COLORS.white : COLORS.black,
                    },
                  ]}>
                  Remenber me
                </Text>
              </View>
            </View>
          </View>
          <Button
            title="Reset Password"
            filled
            onPress={onResetPassword}
            style={styles.button}
          />
          <TouchableOpacity onPress={() => navigate('login')}>
            <Text style={styles.forgotPasswordBtnText}>
              Remenber the password?
            </Text>
          </TouchableOpacity>
          <View></View>
        </ScrollView>
        <View style={styles.bottomContainer}>
          <Text
            style={[
              styles.bottomLeft,
              {
                color: dark ? COLORS.white : COLORS.black,
              },
            ]}>
            Don't have an account ?
          </Text>
          <TouchableOpacity onPress={() => navigate('signup')}>
            <Text style={styles.bottomRight}>{'  '}Sign Up</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: COLORS.white,
  },
  logo: {
    width: 100,
    height: 100,
    tintColor: COLORS.primary,
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 32,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Urbanist Bold',
    color: COLORS.black,
    textAlign: 'center',
  },
  center: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkBoxContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 18,
  },
  checkbox: {
    marginRight: Platform.OS === 'ios' ? 8 : 14,
    height: 16,
    width: 16,
    borderRadius: 4,
    borderColor: COLORS.primary,
    borderWidth: 2,
  },
  privacy: {
    fontSize: 12,
    fontFamily: 'Urbanist Regular',
    color: COLORS.black,
  },
  socialTitle: {
    fontSize: 19.25,
    fontFamily: 'Urbanist Medium',
    color: COLORS.black,
    textAlign: 'center',
    marginVertical: 26,
  },
  socialBtnContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 18,
    position: 'absolute',
    bottom: 12,
    right: 0,
    left: 0,
  },
  bottomLeft: {
    fontSize: 14,
    fontFamily: 'Urbanist Regular',
    color: 'black',
  },
  bottomRight: {
    fontSize: 16,
    fontFamily: 'Urbanist Medium',
    color: COLORS.primary,
  },
  button: {
    marginVertical: 6,
    width: SIZES.width - 32,
    borderRadius: 30,
  },
  forgotPasswordBtnText: {
    fontSize: 16,
    fontFamily: 'Urbanist SemiBold',
    color: COLORS.primary,
    textAlign: 'center',
    marginTop: 12,
  },
});

export default ForgotPasswordEmail;
