import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  ImageSourcePropType,
} from 'react-native';
import React, {useEffect} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ScrollView} from 'react-native-virtualized-view';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {useTheme} from '../theme/ThemeProvider';
import {Image} from 'react-native';
import {COLORS, SIZES, icons, images} from '../constants';
import SubHeaderItem from '../components/SubHeaderItem';
import {transactionHistory} from '../data';
import TransactionHistoryCard from '../components/TransactionHistoryCard';
import {useAppDispatch, useAppSelector} from '../redux/hooks';
import {getRecentParcel} from '../redux/parcels/parcelsSlice';
import LocationItem from '../components/LocationItem';

const Home = () => {
  const {dark, colors} = useTheme();
  const navigation = useNavigation<NavigationProp<any>>();
  const user = useAppSelector(state => state.auth.userData?.userData);
  const token = useAppSelector(state => state.auth.userData?.token);
  const recentParcel = useAppSelector(state => state.parcels.recentParcel);
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (token) {
      dispatch(getRecentParcel(token));
    }
  }, [dispatch, token]);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning 👋';
    if (hour < 17) return 'Good Afternoon 👋';
    if (hour < 20) return 'Good Evening 👋';
    return 'Good Night 🌙';
  };

  /**
   * Render header
   */
  const renderHeader = () => {
    return (
      <View style={styles.headerContainer}>
        <View style={styles.viewLeft}>
          <Image
            source={user && user.imageUrl ? {uri: user.imageUrl} : images.user1}
            resizeMode="contain"
            style={styles.userIcon}
          />
          <View style={styles.viewNameContainer}>
            <Text style={styles.greeeting}>{getGreeting()}</Text>
            <Text
              style={[
                styles.title,
                {
                  color: dark ? COLORS.white : COLORS.greyscale900,
                },
              ]}>
              {user?.name}
            </Text>
          </View>
        </View>
        <View style={styles.viewRight}>
          <TouchableOpacity
            onPress={() => navigation.navigate('notifications')}>
            <Image
              source={icons.notificationBell2 as ImageSourcePropType}
              resizeMode="contain"
              style={[
                styles.bellIcon,
                {tintColor: dark ? COLORS.white : COLORS.greyscale900},
              ]}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  /**
   * Render wallet card
   */
  const renderWalletCard = () => {
    return (
      <View style={styles.cardContainer}>
        <View style={styles.topCardContainer}>
          <View style={styles.topCardLeftContainer}>
            <Text style={styles.cardHolderName}>Andrew Ainsley</Text>
            <Text style={styles.cardNumber}>•••• •••• •••• ••••</Text>
          </View>
          <View style={styles.topCardRightContainer}>
            <Text style={styles.cardType}>VISA</Text>
            <Image
              source={icons.masterCardLogo}
              resizeMode="contain"
              style={styles.cardLogo}
            />
          </View>
        </View>
        <Text style={styles.balanceText}>Your balance</Text>
        <View style={styles.bottomCardContainer}>
          <Text style={styles.amountNumber}>$9,729</Text>
          <TouchableOpacity
            onPress={() => navigation.navigate('topupamount')}
            style={styles.topupBtn}>
            <Image
              source={icons.arrowDownSquare as ImageSourcePropType}
              resizeMode="contain"
              style={styles.arrowDown}
            />
            <Text style={styles.topupBtnText}>Top Up</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  /**
   * Render search bar
   */
  const renderSearchBar = () => {
    const handleInputFocus = () => {
      // Redirect to another screen
      navigation.navigate('trackidnumber');
    };

    return (
      <TouchableOpacity
        onPress={() => navigation.navigate('Search')}
        style={[
          styles.searchBarContainer,
          {
            backgroundColor: dark ? COLORS.dark2 : COLORS.secondaryWhite,
          },
        ]}>
        <TouchableOpacity>
          <Image
            source={icons.search2 as ImageSourcePropType}
            resizeMode="contain"
            style={styles.searchIcon}
          />
        </TouchableOpacity>
        <TextInput
          placeholder="Enter Track ID Number"
          placeholderTextColor={COLORS.gray}
          style={styles.searchInput}
          onFocus={handleInputFocus}
        />
      </TouchableOpacity>
    );
  };

  const renderServices = () => {
    return (
      <View style={{marginTop: 10}}>
        <View style={styles.rowContainer}>
          <TouchableOpacity
            onPress={() => navigation.navigate('orderform')}
            style={styles.serviceContainer}>
            <View style={styles.iconContainer}>
              <Image
                source={icons.editService2}
                resizeMode="contain"
                style={styles.icon}
              />
            </View>
            <Text
              style={[
                styles.serviceTitle,
                {
                  color: dark ? COLORS.white : COLORS.black,
                },
              ]}>
              Make Order
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('topupamount')}
            style={styles.serviceContainer}>
            <View style={styles.iconContainer}>
              <Image
                source={icons.dollarSymbol}
                resizeMode="contain"
                style={styles.icon}
              />
            </View>
            <Text
              style={[
                styles.serviceTitle,
                {
                  color: dark ? COLORS.white : COLORS.black,
                },
              ]}>
              Wallet
            </Text>
          </TouchableOpacity>
        </View>
        <View style={styles.rowContainer}>
          <TouchableOpacity
            onPress={() => navigation.navigate('nearbydrop')}
            style={styles.serviceContainer}>
            <View style={styles.iconContainer}>
              <Image
                source={icons.pin}
                resizeMode="contain"
                style={styles.icon}
              />
            </View>
            <Text
              style={[
                styles.serviceTitle,
                {
                  color: dark ? COLORS.white : COLORS.black,
                },
              ]}>
              Nearby Drop
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('settingshelpcenter')}
            style={styles.serviceContainer}>
            <View style={styles.iconContainer}>
              <Image
                // @ts-expect-error
                source={icons.user}
                resizeMode="contain"
                style={styles.icon}
              />
            </View>
            <Text
              style={[
                styles.serviceTitle,
                {
                  color: dark ? COLORS.white : COLORS.black,
                },
              ]}>
              Help Center
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  /**
   * render transaction history
   */
  const renderTransactionHistory = () => {
    return (
      <View>
        <SubHeaderItem
          title="Recent Parcel"
          navTitle="See All"
          onPress={() => navigation.navigate('Marketplace')}
        />
        <FlatList
          data={recentParcel}
          keyExtractor={item => item.id.toString()}
          renderItem={({item}) => (
            <LocationItem
              description={item.title || item.description}
              amount={item.amount}
              image={item.media.length > 0 ? item.media[0].mediaUrl : ''}
              distance={item.pickup_address}
              parcelDistance={item.delivery_address}
              onPress={() =>
                navigation.navigate('parceldetails', {
                  parcel: item,
                  myOrder: false,
                })
              }
            />
          )}
        />
      </View>
    );
  };
  /**
   * Render home screen
   */
  return (
    <SafeAreaView style={[styles.area, {backgroundColor: colors.background}]}>
      <View style={[styles.container, {backgroundColor: colors.background}]}>
        {renderHeader()}
        <ScrollView
          contentContainerStyle={{paddingBottom: 60}}
          showsVerticalScrollIndicator={false}>
          {/* renderWalletCard() */}
          {/* renderSearchBar() */}
          {renderServices()}
          {renderTransactionHistory()}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    padding: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    width: SIZES.width - 32,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userIcon: {
    width: 48,
    height: 48,
    borderRadius: 32,
  },
  viewLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  greeeting: {
    fontSize: 12,
    fontFamily: 'Urbanist Regular',
    color: 'gray',
    marginBottom: 4,
  },
  title: {
    fontSize: 20,
    fontFamily: 'Urbanist Bold',
    color: COLORS.greyscale900,
  },
  viewNameContainer: {
    marginLeft: 12,
  },
  viewRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bellIcon: {
    height: 24,
    width: 24,
    tintColor: COLORS.black,
    marginRight: 8,
  },
  bookmarkIcon: {
    height: 24,
    width: 24,
    tintColor: COLORS.black,
  },
  cardContainer: {
    width: SIZES.width - 32,
    borderRadius: 32,
    marginTop: 16,
    height: 212,
    backgroundColor: COLORS.primary,
    padding: 16,
  },
  topCardContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  topCardLeftContainer: {
    marginTop: 6,
  },
  topCardRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 22,
  },
  cardHolderName: {
    fontSize: 22,
    color: COLORS.white,
    fontFamily: 'Urbanist Bold',
  },
  cardNumber: {
    fontSize: 20,
    color: COLORS.white,
    fontFamily: 'Urbanist SemiBold',
  },
  cardType: {
    fontSize: 26,
    color: COLORS.white,
    fontFamily: 'extraBoldItalic',
  },
  cardLogo: {
    height: 52,
    width: 52,
    marginLeft: 6,
  },
  balanceText: {
    fontSize: 18,
    color: COLORS.white,
    fontFamily: 'Urbanist Medium',
  },
  bottomCardContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 22,
  },
  amountNumber: {
    fontSize: 42,
    color: COLORS.white,
    fontFamily: 'Urbanist Bold',
  },
  topupBtn: {
    width: 132,
    height: 42,
    backgroundColor: COLORS.white,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  arrowDown: {
    width: 16,
    height: 16,
    tintColor: COLORS.primary,
  },
  topupBtnText: {
    fontSize: 16,
    color: COLORS.primary,
    fontFamily: 'Urbanist SemiBold',
    marginLeft: 12,
  },
  searchBarContainer: {
    width: SIZES.width - 32,
    backgroundColor: COLORS.secondaryWhite,
    padding: 16,
    borderRadius: 12,
    height: 52,
    marginVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchIcon: {
    height: 24,
    width: 24,
    tintColor: COLORS.gray,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Urbanist Regular',
    marginHorizontal: 8,
  },
  filterIcon: {
    width: 24,
    height: 24,
    tintColor: COLORS.primary,
  },
  rowContainer: {
    width: SIZES.width - 32,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 8,
  },
  serviceContainer: {
    width: (SIZES.width - 52) / 2,
    height: 120,
    borderWidth: 1,
    borderColor: 'rgba(34, 187, 156, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 16,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(34, 187, 156, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    height: 24,
    width: 24,
    tintColor: COLORS.primary,
  },
  serviceTitle: {
    fontSize: 16,
    color: COLORS.black,
    fontFamily: 'Urbanist SemiBold',
    textAlign: 'center',
    marginTop: 8,
  },
});

export default Home;
