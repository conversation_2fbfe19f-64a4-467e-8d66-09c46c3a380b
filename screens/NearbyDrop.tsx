import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  TextInput,
  FlatList,
  Switch,
  ImageSourcePropType,
} from 'react-native';
import React, {useState, useRef, useCallback, useEffect} from 'react';
import {COLORS, SIZES, icons} from '../constants';
import {SafeAreaView} from 'react-native-safe-area-context';
import Feather from 'react-native-vector-icons/Feather';
import {
  NavigationProp,
  useFocusEffect,
  useNavigation,
} from '@react-navigation/native';
import {locations} from '../data';
import {useTheme} from '../theme/ThemeProvider';
import LocationItem from '../components/LocationItem';
import RBSheet from 'react-native-raw-bottom-sheet';
import Slider from '@react-native-community/slider';
import {useAppDispatch, useAppSelector} from '../redux/hooks';
import {getMarketPlace} from '../redux/parcels/parcelsSlice';

const NearbyDrop = () => {
  const navigation = useNavigation<NavigationProp<any>>();
  const {dark, colors} = useTheme();
  const [searchInput, setSearchInput] = useState('');
  const parcelList = useAppSelector(state => state.parcels.marketPlaceList);
  // static const [filteredLocations, setFilteredLocations] = useState(locations);
  const [filteredLocations, setFilteredLocations] = useState<any>(parcelList);
  const token = useAppSelector(state => state.auth.userData?.token);
  const bottomSheetRef = useRef<any>(null);
  const dispatch = useAppDispatch();

  // Filter States
  const [pickupDistance, setPickupDistance] = useState(50);
  const [parcelDistance, setParcelDistance] = useState(50);
  const [deliveryLocation, setDeliveryLocation] = useState('');
  const [keyword, setKeyword] = useState('');
  const [insured, setInsured] = useState(false);

  useEffect(() => {
    setFilteredLocations(parcelList);
  }, [parcelList]);

  useFocusEffect(
    useCallback(() => {
      if (token) {
        dispatch(getMarketPlace(token));
      }
      return () => {};
    }, [token]),
  );

  const handleSearchInput = (text: string) => {
    setSearchInput(text);
    applyFilters({keyword: text});
  };

  const applyFilters = (overrideFilters = {}) => {
    const filters = {
      pickupDistance,
      parcelDistance,
      deliveryLocation,
      keyword,
      insured,
      ...overrideFilters,
    };

    const filtered = locations.filter(location => {
      return (
        location.pickup_distance <= filters.pickupDistance &&
        location.parcel_distance <= filters.parcelDistance &&
        (filters.keyword
          ? location.description
              .toLowerCase()
              .includes(filters.keyword.toLowerCase()) ||
            location.pickup_address
              .toLowerCase()
              .includes(filters.keyword.toLowerCase())
          : true) &&
        (filters.deliveryLocation
          ? location.delivery_address
              .toLowerCase()
              .includes(filters.deliveryLocation.toLowerCase())
          : true) &&
        (filters.insured ? location.insured === true : true)
      );
    });

    setFilteredLocations(filtered);
  };

  const openFilterSheet = () => {
    bottomSheetRef.current?.open();
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={styles.headerIconContainer}>
        <Image
          source={icons.arrowLeft as ImageSourcePropType}
          resizeMode="contain"
          style={styles.arrowBackIcon}
        />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Market Place</Text>
      <TouchableOpacity onPress={openFilterSheet}>
        <Feather name="filter" size={20} color={COLORS.primary} />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={[styles.area, {backgroundColor: colors.background}]}>
      <View style={[styles.container, {backgroundColor: colors.background}]}>
        {renderHeader()}
        <View style={styles.searchBarContainer}>
          <Feather name="search" size={24} color={COLORS.gray} />
          <TextInput
            placeholder="Search Location"
            placeholderTextColor={COLORS.gray}
            style={styles.searchInput}
            value={searchInput}
            onChangeText={handleSearchInput}
          />
        </View>

        <FlatList
          data={filteredLocations}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={{paddingTop: 10, paddingBottom: 10}}
          showsVerticalScrollIndicator={false}
          renderItem={({item}) => (
            <LocationItem
              description={item.title || item.description}
              amount={item.amount}
              image={item.media.length > 0 ? item.media[0].mediaUrl : ''}
              distance={item.pickup_address}
              parcelDistance={item.delivery_address}
              onPress={() =>
                navigation.navigate('parceldetails', {
                  parcel: item,
                  myOrder: false,
                })
              }
            />
          )}
        />

        {/* Bottom Sheet for Filters */}
        <RBSheet
          ref={bottomSheetRef}
          height={470}
          openDuration={250}
          closeOnPressMask={true}
          customStyles={{
            container: styles.sheetContainer,
          }}>
          <View style={styles.filterContainer}>
            <Text style={styles.filterTitle}>Filter Options</Text>

            {/* Pickup Distance Filter */}
            {false && (
              <View style={styles.filterItem}>
                <Text style={styles.filterLabel}>
                  Pickup Distance: {pickupDistance} KM
                </Text>
                <Slider
                  minimumValue={1}
                  maximumValue={100}
                  step={1}
                  value={pickupDistance}
                  onValueChange={value => setPickupDistance(value)}
                  minimumTrackTintColor={COLORS.primary}
                  thumbTintColor={COLORS.primary}
                />
              </View>
            )}

            {/* Parcel Distance Filter */}
            <View style={styles.filterItem}>
              <Text style={styles.filterLabel}>
                Distance from pickup: {parcelDistance} KM
              </Text>
              <Slider
                minimumValue={1}
                maximumValue={100}
                step={1}
                value={parcelDistance}
                onValueChange={value => setParcelDistance(value)}
                minimumTrackTintColor={COLORS.primary}
                thumbTintColor={COLORS.primary}
              />
            </View>
            {/* Pickup Location Filter */}
            <TextInput
              placeholder="Pickup Location"
              style={styles.input}
              value={deliveryLocation}
              onChangeText={setDeliveryLocation}
              placeholderTextColor="#888"
            />

            {/* Delivery Location Filter */}
            <TextInput
              placeholder="Delivery Location"
              style={styles.input}
              value={deliveryLocation}
              onChangeText={setDeliveryLocation}
              placeholderTextColor="#888"
            />

            {/* Keyword Search Filter */}
            <TextInput
              placeholder="Keyword"
              style={styles.input}
              value={keyword}
              onChangeText={setKeyword}
              placeholderTextColor="#888"
            />

            {/* Insured Toggle */}
            <View style={styles.switchContainer}>
              <Text style={styles.filterLabel}>Insured</Text>
              <Switch
                value={insured}
                onValueChange={setInsured}
                thumbColor={insured ? COLORS.primary : '#ccc'}
                trackColor={{false: '#ddd', true: '#80bdff'}}
              />
            </View>

            {/* Apply Filters Button */}
            <TouchableOpacity
              style={styles.applyButton}
              onPress={() => {
                applyFilters();
                bottomSheetRef.current?.close();
              }}>
              <Text style={styles.applyButtonText}>Apply Filters</Text>
            </TouchableOpacity>
          </View>
        </RBSheet>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  sheetContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    backgroundColor: '#fff',
  },
  filterContainer: {
    flex: 1,
  },
  filterTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
    color: '#333',
  },
  filterItem: {
    marginBottom: 15,
    backgroundColor: '#f8f9fa',
    padding: 10,
    borderRadius: 10,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
    color: '#555',
  },
  input: {
    backgroundColor: '#f1f3f5',
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    marginBottom: 15,
    color: '#333',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    marginBottom: 15,
  },
  applyButton: {
    backgroundColor: COLORS.primary,
    padding: 12,
    borderRadius: 10,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  area: {flex: 1},
  container: {flex: 1},
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
  },
  headerIconContainer: {padding: 5},
  arrowBackIcon: {width: 20, height: 20},
  headerTitle: {fontSize: 18, fontWeight: 'bold'},
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.grayscale100,
    paddingHorizontal: 10,
    borderRadius: 10,
    overflow: 'hidden',
    marginHorizontal: 10,
  },
  searchInput: {flex: 1, marginLeft: 10},
});

export default NearbyDrop;
