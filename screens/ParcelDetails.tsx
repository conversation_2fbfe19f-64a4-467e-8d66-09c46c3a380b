import {
  NavigationProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  ScrollView,
  TouchableOpacity,
  ImageSourcePropType,
  Alert,
} from 'react-native';
import MapView, {Marker} from 'react-native-maps';
import MapViewDirections from 'react-native-maps-directions';
import Button from '../components/Button';
import {COLORS, icons} from '../constants';
import {useTheme} from '../theme/ThemeProvider';
import Feather from 'react-native-vector-icons/Feather';
import {SafeAreaView} from 'react-native-safe-area-context';
import ParcelDeliveryTimer from '../components/ParcelDeliveryTimer';
import ParcelCancelTimer from '../components/ParcelCancelTimer';
import {useAppDispatch, useAppSelector} from '../redux/hooks';
import {acceptParcel, cancelParcel, getParcelById, Parcel} from '../redux/parcels/parcelsSlice';

const ParcelDetail = () => {
  const route = useRoute<any>();
  const navigation = useNavigation<NavigationProp<any>>();
  const {dark, colors} = useTheme();
  const user = useAppSelector(state => state.auth.userData?.userData);
  const myOrderA = route.params.myOrder;
  const parcelA = route.params.parcel;
  const [myOrder, setMyOrder] = useState(myOrderA);
  const [parcel, setParcel] = useState<Parcel>(parcelA);
  const mapRef = useRef<MapView>(null);
  const pickupRef = useRef<any>(null);
  const deliveryRef = useRef<any>(null);
  const [status, setStatus] = useState(parcel.status);
  const token = useAppSelector(state => state.auth.userData?.token);
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (parcel && user) {
      setMyOrder(parcel.userId == user.id);
    }
  }, [parcel, user]);

  useFocusEffect(
    useCallback(() => {
      if (parcelA && token) {
        dispatch(getParcelById({token: token, id: parcelA.id}))
          .unwrap()
          .then((response: any) => {
            if (response.parcel && response.parcel.id) {
              setStatus(response.parcel.status);
              setParcel(response.parcel);
            }
          })
          .catch((error: any) => {
            console.error('Error fetching parcel details:', error);
          });
      }

      // optional cleanup if needed
      return () => {};
    }, [parcelA, token]),
  );

  const pickupCoords = {
    latitude: parseFloat(parcel.pickup_lat),
    longitude: parseFloat(parcel.pickup_long),
  };

  const deliveryCoords = {
    latitude: parseFloat(parcel.delivery_lat),
    longitude: parseFloat(parcel.delivery_long),
  };

  useEffect(() => {
    setTimeout(() => {
      if (mapRef.current) {
        mapRef.current.fitToCoordinates([pickupCoords, deliveryCoords], {
          edgePadding: {top: 20, right: 20, bottom: 20, left: 20},
          animated: true,
        });
      }
    }, 1000); // Delay to ensure the map is ready
  }, [parcel, mapRef]);

  const renderHeader = () => {
    return (
      <View style={styles.headerContainer}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={[
            styles.headerIconContainer,
            {
              borderColor: dark ? 'transparent' : COLORS.grayscale200,
            },
          ]}>
          <Image
            source={icons.arrowLeft as ImageSourcePropType}
            resizeMode="contain"
            style={[
              styles.arrowBackIcon,
              {
                tintColor: dark ? COLORS.white : COLORS.black,
              },
            ]}
          />
        </TouchableOpacity>
        <Text
          style={[
            styles.headerTitle,
            {
              color: dark ? COLORS.white : COLORS.black,
            },
          ]}>
          Parcel Details
        </Text>
        <TouchableOpacity>
          <Feather
            name="more-vertical"
            size={24}
            color={dark ? COLORS.white : 'black'}
          />
        </TouchableOpacity>
      </View>
    );
  };

  const updateParcel = () => {
    dispatch(getParcelById({token: token, id: parcel.id}))
      .unwrap()
      .then((response: any) => {
        if (response.parcel && response.parcel.id) {
          setStatus(response.parcel.status);
          setParcel(response.parcel);
        }
      })
      .catch((error: any) => {
        console.error('Error fetching parcel details:', error);
      });
  };

  const onParcelAccepted = () => {
    if (status == 'pending' && !myOrder) {
      dispatch(acceptParcel({token: token, id: parcel.id}))
        .unwrap()
        .then((response: any) => {
          if (response.acceptance) {
            updateParcel();
            Alert.alert(
              'Parcel Accepted',
              'Please collect the parcel from the pickup location and deliver it to the destination.',
              [
                {
                  text: 'OK',
                  onPress: () => {},
                },
              ],
            );
          } else {
            Alert.alert('Error', response.message, [
              {
                text: 'OK',
                onPress: () => {},
              },
            ]);
          }
        })
        .catch((error: any) => {
          Alert.alert('Error', error.message || 'Something went wrong.', [
            {
              text: 'OK',
              onPress: () => {},
            },
          ]);
          console.error('Error accepting parcel:', error);
        });
    } else {
      parcel.status = status;
      navigation.navigate('parcellive', {parcel: parcel, myOrder: myOrder});
    }
  };

  const onEditParcelClick = () => {
    navigation.navigate('orderform', {parcel: parcel, myOrder: myOrder});
  };

  const onCancelParcel = () => {
    if (token && parcel.id) {
      dispatch(cancelParcel({token: token, id: parcel.id.toString()}))
        .unwrap()
        .then((response: any) => {
          if (response.success) {
            updateParcel();
            Alert.alert(
              'Parcel Cancelled',
              'The parcel has been successfully cancelled and returned to the marketplace.',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    navigation.goBack();
                  },
                },
              ],
            );
          } else {
            Alert.alert('Error', response.message || 'Failed to cancel parcel', [
              {
                text: 'OK',
                onPress: () => {},
              },
            ]);
          }
        })
        .catch((error: any) => {
          Alert.alert('Error', error.message || 'Something went wrong.', [
            {
              text: 'OK',
              onPress: () => {},
            },
          ]);
          console.error('Error cancelling parcel:', error);
        });
    }
  };

  const onCancelTimeExpired = () => {
    // Just update the parcel to refresh the UI
    updateParcel();
  };

  return (
    <SafeAreaView style={[styles.area, {backgroundColor: colors.background}]}>
      <View style={[styles.containerA, {backgroundColor: colors.background}]}>
        {renderHeader()}
        <ScrollView
          contentContainerStyle={[
            styles.container,
            {backgroundColor: dark ? COLORS.dark3 : COLORS.white},
          ]}>
          {/* Parcel Information */}
          <View style={styles.detailsContainer}>
            {parcel.title && (
              <Text
                style={[
                  styles.description,
                  {color: dark ? COLORS.white : COLORS.dark3},
                ]}>
                {parcel.title}
              </Text>
            )}
            <Text
              style={[
                styles.description,
                {
                  color: dark ? COLORS.white : COLORS.dark3,
                  fontWeight: 'normal',
                  fontSize: 15,
                },
              ]}>
              {parcel.description}
            </Text>
            <Text
              style={[
                styles.text,
                {color: dark ? COLORS.white : COLORS.dark3},
              ]}>
              Category:{' '}
              <Text style={{fontWeight: 'bold'}}>{parcel.category?.name}</Text>
            </Text>
            <Text
              style={[
                styles.text,
                {color: dark ? COLORS.white : COLORS.dark3},
              ]}>
              Package Type:{' '}
              <Text style={{fontWeight: 'bold'}}>{parcel.package_type}</Text>
            </Text>
            <Text
              style={[
                styles.text,
                {color: dark ? COLORS.white : COLORS.dark3},
              ]}>
              Weight: <Text style={{fontWeight: 'bold'}}>{parcel.weight}</Text>
            </Text>
            <View style={styles.rowView}>
              <Text
                style={[
                  styles.text,
                  {
                    color:
                      status === 'pending'
                        ? 'red'
                        : status === 'accepted'
                        ? '#ebab34'
                        : 'green',
                  },
                ]}>
                Status: {status}
              </Text>
              <Text
                style={[
                  styles.text,
                  {color: dark ? COLORS.white : COLORS.dark3},
                ]}>
                Amount:{' '}
                <Text style={{fontWeight: 'bold'}}>AED {parcel.amount}</Text>
              </Text>
            </View>
          </View>

          {status == 'accepted' && !myOrder && parcel.acceptance?.accepted_at && (
            <ParcelCancelTimer
              acceptedAt={parcel.acceptance.accepted_at}
              onCancel={onCancelParcel}
              onTimeExpired={onCancelTimeExpired}
            />
          )}

          {status == 'accepted' && (
            <ParcelDeliveryTimer
              acceptedAt={parcel.acceptance?.accepted_at}
              onReturnToMarketplace={() => {}}
              showExtend={!myOrder}
            />
          )}

          {/* Parcel Image */}
          <Text style={styles.imageTitle}>Parcel Image:</Text>
          <FlatList
            data={parcel.media}
            horizontal
            style={styles.imageList}
            showsHorizontalScrollIndicator={false}
            keyExtractor={(_, index) => index.toString()}
            renderItem={({item}) => (
              <Image
                resizeMode={'cover'}
                source={{uri: item.mediaUrl}}
                style={styles.parcelImage}
              />
            )}
          />
          {/* Sender Details */}
          {parcel.user && parcel.user.name && (
            <View style={styles.senderContainer}>
              <Text style={styles.senderTitle}>Sender Information</Text>
              <Text style={styles.senderText}>Name: {parcel.user.name}</Text>
              <Text style={styles.senderText}>
                Phone: {parcel.user.profile.phone_number}
              </Text>
            </View>
          )}

          {/* Sender Details */}
          {myOrder && status != 'pending' && (
            <View style={styles.senderContainer}>
              <Text style={styles.senderTitle}>Delievry User Information</Text>
              <Text style={styles.senderText}>
                Name: {parcel.acceptance?.deliveryUser?.name}
              </Text>
              <Text style={styles.senderText}>
                Email: {parcel.acceptance?.deliveryUser?.email}
              </Text>
              {status == 'accepted' && (
                <Text style={[styles.senderText, {fontWeight: 'bold'}]}>
                  OTP: {parcel.acceptance?.otp}
                </Text>
              )}
            </View>
          )}

          {parcel.acceptance &&
            parcel.acceptance.delivery_user_id == user?.id && (
              <View style={styles.senderContainer}>
                <Text style={styles.senderTitle}>Receiver Information</Text>
                <Text style={styles.senderText}>Name: {parcel.full_name}</Text>
                <Text style={styles.senderText}>Phone: {parcel.mobile}</Text>
                <Text style={styles.senderText}>Email: {parcel.email}</Text>
              </View>
            )}
          {/* Address Details */}
          <View style={styles.addressContainer}>
            <Text style={styles.addressTitle}>Pickup Address</Text>
            <Text style={styles.addressText}>{parcel.pickup_address}</Text>
            <Text style={styles.addressTitle}>Delivery Address</Text>
            <Text style={styles.addressText}>{parcel.delivery_address}</Text>
          </View>
          <MapView
            ref={mapRef}
            style={styles.map}
            initialRegion={{
              latitude: parseFloat(parcel.pickup_lat),
              longitude: parseFloat(parcel.pickup_long),
              latitudeDelta: 0.05,
              longitudeDelta: 0.05,
            }}>
            <MapViewDirections
              origin={{
                latitude: parseFloat(parcel.pickup_lat),
                longitude: parseFloat(parcel.pickup_long),
              }}
              destination={{
                latitude: parseFloat(parcel.delivery_lat),
                longitude: parseFloat(parcel.delivery_long),
              }}
              apikey={'GOOGLE_API_KEY'}
              strokeWidth={4}
              strokeColor="blue"
              mode="DRIVING"
            />
            <Marker
              ref={pickupRef}
              coordinate={{
                latitude: parseFloat(parcel.pickup_lat),
                longitude: parseFloat(parcel.pickup_long),
              }}
              title="Pickup Location"
              description={parcel.pickup_address}
            />
            <Marker
              ref={deliveryRef}
              coordinate={{
                latitude: parseFloat(parcel.delivery_lat),
                longitude: parseFloat(parcel.delivery_long),
              }}
              title="Delivery Location"
              description={parcel.delivery_address}
            />
          </MapView>
        </ScrollView>

        {/* Action Buttons */}
        <View
          style={[
            styles.rowView,
            {
              paddingHorizontal: 10,
              paddingTop: 10,
              backgroundColor: colors.background,
            },
          ]}>
          <Button
            title={
              status == 'pending' && !myOrder ? 'Accept Parcel' : 'Track Parcel'
            }
            filled
            style={styles.button}
            onPress={onParcelAccepted}
          />
          <Button
            title={
              myOrder && status == 'pending' ? 'Edit Parcel' : 'Get Directions'
            }
            textColor={dark ? COLORS.primary : COLORS.black}
            style={styles.buttonOutline}
            onPress={onEditParcelClick}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  acceptedContainer: {
    width: '100%',
    padding: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 8,
    marginVertical: 10,
  },
  containerA: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  headerIconContainer: {
    height: 46,
    width: 46,
    borderWidth: 1,
    borderColor: COLORS.grayscale200,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 999,
  },
  arrowBackIcon: {
    width: 24,
    height: 24,
    tintColor: COLORS.black,
  },
  headerTitle: {
    fontSize: 16,
    fontFamily: 'Urbanist Bold',
    color: COLORS.black,
  },
  area: {
    flex: 1,
    backgroundColor: COLORS.white,
    paddingBottom: 40,
  },
  imageTitle: {
    fontWeight: '700',
    color: COLORS.black,
  },
  rowView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  container: {
    padding: 16,
    flexGrow: 1,
  },
  imageList: {
    marginTop: 10,
    alignSelf: 'flex-start',
  },
  map: {
    width: '100%',
    height: 250,
    borderRadius: 10,
  },
  parcelImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginRight: 10,
    overflow: 'hidden',
  },
  detailsContainer: {
    marginBottom: 10,
    width: '100%',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  description: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  text: {
    fontSize: 16,
    marginVertical: 2,
  },
  addressContainer: {
    width: '100%',
    padding: 10,
    backgroundColor: '#EFEFEF',
    borderRadius: 8,
    marginVertical: 10,
  },
  addressTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  addressText: {
    fontSize: 14,
    color: COLORS.gray,
  },
  senderContainer: {
    width: '100%',
    padding: 10,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 8,
    marginVertical: 10,
  },
  senderTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  senderText: {
    fontSize: 14,
    color: COLORS.black,
  },
  button: {
    width: '47%',
    borderRadius: 8,
    paddingVertical: 10,
    height: undefined,
  },
  buttonOutline: {
    width: '47%',
    borderColor: COLORS.primary,
    borderRadius: 8,
    paddingVertical: 10,
    height: undefined,
  },
});

export default ParcelDetail;
