import { View, Text, TouchableOpacity, Image, useWindowDimensions, ImageSourcePropType, StyleSheet } from 'react-native';
import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TabView, SceneMap, TabBar } from 'react-native-tab-view';
import { FromMeRoute, ToMeRoute } from '../tabs';
import { useTheme } from '../theme/ThemeProvider';
import { COLORS, icons, images } from '../constants';

const renderScene = SceneMap({
  first: FromMeRoute,
  second: ToMeRoute,
});

const MyOrder = () => {
  const { dark, colors } = useTheme();
  const layout = useWindowDimensions();

  const [index, setIndex] = React.useState(0);

  const [routes] = React.useState([
    { key: 'first', title: 'My Parcel' },
    { key: 'second', title: 'Accepted Parcel' },
  ])

  const renderTabBar = (props:any) => (
    <TabBar
      {...props}
      indicatorStyle={{
        backgroundColor: COLORS.primary
      }}
      style={{
        backgroundColor: colors.background,
      }}
      renderLabel={({ route, focused }) => (
        <Text style={[{ 
          color: focused ? COLORS.primary : 'gray',
          fontSize: 16,
          fontFamily: "Urbanist Bold"
          }]}>
          {route.title}
        </Text>
      )}
    />
  );

   /**
   * render header
   */
    const renderHeader = () => {
      return (
        <View style={styles.headerContainer}>
          <View style={styles.headerLeft}>
            <Image
              source={images.logo as ImageSourcePropType}
              resizeMode='contain'
              style={styles.headerLogo}
            />
            <Text style={[styles.headerTitle, {
              color: dark ? COLORS.white : COLORS.greyscale900
            }]}>My Parcels</Text>
          </View>
          <View style={styles.headerRight}>
            <TouchableOpacity>
              <Image
                source={icons.moreCircle as ImageSourcePropType}
                resizeMode='contain'
                style={[styles.moreCircleIcon, {
                  tintColor: dark ? COLORS.secondaryWhite : COLORS.greyscale900
                }]}
              />
            </TouchableOpacity>
          </View>
        </View>
      )
    }

    
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        {renderHeader()}
        <View style={{ flex: 1, marginHorizontal: 22 }}>
          <TabView
            navigationState={{ index, routes }}
            renderScene={renderScene}
            onIndexChange={setIndex}
            initialLayout={{ width: layout.width }}
            renderTabBar={renderTabBar}
          />
        </View>
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    justifyContent: "space-between",
    paddingHorizontal: 16
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center"
  },
  headerLogo: {
    height: 36,
    width: 36,
    tintColor: COLORS.primary
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: "Urbanist Bold",
    color: COLORS.black,
    marginLeft: 12
  },
  headerRight: {
    flexDirection: "row",
    alignItems: "center"
  },
  searchIcon: {
    width: 24,
    height: 24,
    tintColor: COLORS.black
  },
  moreCircleIcon: {
    width: 24,
    height: 24,
    tintColor: COLORS.black,
    marginLeft: 12
  },
})
export default MyOrder