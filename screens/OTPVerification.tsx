import {View, Text, StyleSheet, ScrollView, Alert} from 'react-native';
import React, {useEffect, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import Header from '../components/Header';
import {COLORS} from '../constants';
import {OtpInput} from 'react-native-otp-entry';
import Button from '../components/Button';
import {useTheme} from '../theme/ThemeProvider';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useAppDispatch} from '../redux/hooks';
import {saveUserData, User, verifyOTP} from '../redux/auth/authSlice';

type Nav = {
  navigate: (value: string, payload: any) => void;
};

// OTP Verification Screen
const OTPVerification = () => {
  const {navigate} = useNavigation<Nav>();
  const route = useRoute<any>();
  const [time, setTime] = useState(50);
  const {colors, dark} = useTheme();
  const [otp, setOTP] = useState('');
  const dispatch = useAppDispatch();
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    const intervalId = setInterval(() => {
      setTime(prevTime => (prevTime > 0 ? prevTime - 1 : 0));
    }, 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  const onVerifyCode = async () => {
    if (otp.length < 6) {
      Alert.alert('Please enter complete verification code.');
      return;
    }
    setLoading(true);
    dispatch(verifyOTP({email: route.params?.email, otp: otp}))
      .unwrap()
      .then((data: User) => {
        if (data.status) {
          if (route.params?.isLogin) {
            dispatch(saveUserData(data));
            navigate('reasonforusingsaska', {user: data});
          } else {
            navigate('createnewpassword', {user: data});
          }
        } else {
          Alert.alert('Error', data?.message || 'Something went wrong');
        }
        setLoading(false);
      })
      .catch((err: any) => {
        Alert.alert('Error', err?.message || 'Something went wrong');
        setLoading(false);
      });
  };

  return (
    <SafeAreaView style={[styles.area, {backgroundColor: colors.background}]}>
      <View style={[styles.container, {backgroundColor: colors.background}]}>
        <Header title="Forgot Password" />
        <ScrollView>
          <Text
            style={[
              styles.title,
              {
                color: dark ? COLORS.white : COLORS.black,
              },
            ]}>
            Code has been send to {'\n' + route.params?.email || ''}
          </Text>
          <OtpInput
            numberOfDigits={6}
            onTextChange={setOTP}
            focusColor={COLORS.primary}
            focusStickBlinkingDuration={500}
            onFilled={setOTP}
            theme={{
              pinCodeContainerStyle: {
                backgroundColor: dark ? COLORS.dark2 : COLORS.secondaryWhite,
                borderColor: dark ? COLORS.gray : COLORS.secondaryWhite,
                borderWidth: 0.4,
                borderRadius: 10,
                height: 58,
                width: 58,
              },
              pinCodeTextStyle: {
                color: dark ? COLORS.white : COLORS.black,
              },
            }}
          />
          <View style={styles.codeContainer}>
            <Text
              style={[
                styles.code,
                {
                  color: dark ? COLORS.white : COLORS.greyscale900,
                },
              ]}>
              Resend code in
            </Text>
            <Text style={styles.time}>{`  ${time} `}</Text>
            <Text
              style={[
                styles.code,
                {
                  color: dark ? COLORS.white : COLORS.greyscale900,
                },
              ]}>
              s
            </Text>
          </View>
        </ScrollView>
        <Button
          title="Verify"
          filled
          style={styles.button}
          isLoading={isLoading}
          onPress={onVerifyCode}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: COLORS.white,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Urbanist Medium',
    color: COLORS.greyscale900,
    textAlign: 'center',
    marginVertical: 54,
  },
  OTPStyle: {
    borderRadius: 8,
    height: 58,
    width: 58,
    backgroundColor: COLORS.white,
    borderBottomColor: 'gray',
    borderBottomWidth: 0.4,
    borderWidth: 0.4,
    borderColor: 'gray',
  },
  codeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
    justifyContent: 'center',
  },
  code: {
    fontSize: 18,
    fontFamily: 'Urbanist Medium',
    color: COLORS.greyscale900,
    textAlign: 'center',
  },
  time: {
    fontFamily: 'Urbanist Medium',
    fontSize: 18,
    color: COLORS.primary,
  },
  button: {
    borderRadius: 32,
  },
});

export default OTPVerification;
