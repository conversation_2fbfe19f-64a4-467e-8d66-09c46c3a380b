import React, {useEffect, useState} from 'react';
import {View, Image, StyleSheet} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {COLORS, SIZES} from '../constants';
import {useTheme} from '../theme/ThemeProvider';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAppSelector} from '../redux/hooks';

const Splash = () => {
  const {colors} = useTheme();
  const navigation = useNavigation<NavigationProp<any>>();
  const userData = useAppSelector(state => state.auth.userData);

  const checkIfFirstLaunch = async () => {
    try {
      const value = await AsyncStorage.getItem('alreadyLaunched');
      if (value === null) {
        await AsyncStorage.setItem('alreadyLaunched', 'true');
        navigation.navigate('onboarding1');
      } else {
        navigation.navigate('welcome');
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (userData) {
      setTimeout(() => {
        if (
          userData.userData?.status == 'active' ||
          userData.userData?.status == 'approved'
        ) {
          navigation.navigate('Main');
        } else {
          checkIfFirstLaunch();
        }
      }, 3000);
    } else {
      setTimeout(() => {
        checkIfFirstLaunch();
      }, 3000);
    }
  }, []);

  return (
    <SafeAreaView style={[styles.area, {backgroundColor: colors.background}]}>
      <View style={styles.container}>
        <Image
          source={require('../assets/images/splash.png')}
          resizeMode={'cover'}
          style={styles.image}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: SIZES.width,
    height: SIZES.height,
  },
});

export default Splash;
