import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  Alert,
  TouchableWithoutFeedback,
  Modal,
  Platform,
} from 'react-native';
import React, {useCallback, useEffect, useReducer, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {COLORS, SIZES, icons, illustrations} from '../constants';
import Header from '../components/Header';
import {reducer} from '../utils/reducers/formReducers';
import {validateInput} from '../utils/actions/formActions';
import Input from '../components/Input';
import CheckBox from '@react-native-community/checkbox';
import Button from '../components/Button';
import {useTheme} from '../theme/ThemeProvider';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useAppDispatch} from '../redux/hooks';
import {changePassword, User} from '../redux/auth/authSlice';

type Nav = {
  navigate: (value: string) => void;
};

const isTestMode = false;

const initialState = {
  inputValues: {
    newPassword: isTestMode ? '**********' : '',
    confirmNewPassword: isTestMode ? '**********' : '',
  },
  inputValidities: {
    newPassword: false,
    confirmNewPassword: false,
  },
  formIsValid: false,
};

const CreateNewPassword = () => {
  const {navigate} = useNavigation<Nav>();
  const route = useRoute<any>();
  const [formState, dispatchFormState] = useReducer(reducer, initialState);
  const [error, setError] = useState(null);
  const [isChecked, setChecked] = useState(false);
  const {colors, dark} = useTheme();
  const dispatch = useAppDispatch();
  const [isLoading, setLoading] = useState(false);

  const inputChangedHandler = useCallback(
    (inputId: string, inputValue: string) => {
      const result = validateInput(inputId, inputValue);
      dispatchFormState({
        inputId,
        validationResult: result,
        inputValue,
      });
    },
    [dispatchFormState],
  );

  useEffect(() => {
    if (error) {
      Alert.alert('An error occured', error);
    }
  }, [error]);

  const onChangePassword = () => {
    const user: User = route.params.user;
    if (!user) {
      Alert.alert('Something went wrong');
      return;
    }
    if (
      formState.inputValues.newPassword !=
      formState.inputValues.confirmNewPassword
    ) {
      Alert.alert('New password and confirm new password does not match.');
      return;
    }
    if (formState.formIsValid) {
      setLoading(true);
      dispatch(
        changePassword({
          newPassword: formState.inputValues.newPassword,
          confirmPassword: formState.inputValues.confirmNewPassword,
          token: user.token,
        }),
      )
        .unwrap()
        .then((data: User) => {
          if (data.status) {
            Alert.alert('Success', 'Your password is changed. please login with new password.');
            navigate('login');
          } else {
            Alert.alert('Error', data?.message || 'Something went wrong');
          }
          setLoading(false);
        })
        .catch((err: any) => {
          Alert.alert('Error', err?.message || 'Something went wrong');
          setLoading(false);
        });
    } else {
      inputChangedHandler('newPassword', formState.inputValues.newPassword);
      inputChangedHandler(
        'confirmNewPassword',
        formState.inputValues.confirmNewPassword,
      );
    }
  };

  return (
    <SafeAreaView style={[styles.area, {backgroundColor: colors.background}]}>
      <View style={[styles.container, {backgroundColor: colors.background}]}>
        <Header title="Create New Password" />
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.logoContainer}>
            <Image
              source={
                dark
                  ? illustrations.passwordSuccessDark
                  : illustrations.newPassword
              }
              resizeMode="contain"
              style={styles.success}
            />
          </View>
          <Text
            style={[
              styles.title,
              {
                color: dark ? COLORS.white : COLORS.black,
              },
            ]}>
            Create Your New Password
          </Text>
          <Input
            onInputChanged={inputChangedHandler}
            errorText={formState.inputValidities['newPassword']}
            autoCapitalize="none"
            id="newPassword"
            placeholder="New Password"
            placeholderTextColor={dark ? COLORS.grayTie : COLORS.black}
            icon={icons.padlock}
            secureTextEntry={true}
          />
          <Input
            onInputChanged={inputChangedHandler}
            errorText={formState.inputValidities['confirmNewPassword']}
            autoCapitalize="none"
            id="confirmNewPassword"
            placeholder="Confirm New Password"
            placeholderTextColor={dark ? COLORS.grayTie : COLORS.black}
            icon={icons.padlock}
            secureTextEntry={true}
          />
          <View style={styles.checkBoxContainer}>
            <View style={{flexDirection: 'row'}}>
              <CheckBox
                style={styles.checkbox}
                value={isChecked}
                boxType="square"
                onTintColor={
                  isChecked ? COLORS.primary : dark ? COLORS.primary : 'gray'
                }
                onFillColor={
                  isChecked ? COLORS.primary : dark ? COLORS.primary : 'gray'
                }
                onCheckColor={COLORS.white}
                onValueChange={setChecked}
                tintColors={{true: COLORS.primary, false: 'gray'}}
              />
              <View style={{flex: 1}}>
                <Text
                  style={[
                    styles.privacy,
                    {
                      color: dark ? COLORS.white : COLORS.black,
                    },
                  ]}>
                  Remenber me
                </Text>
              </View>
            </View>
          </View>
          <View></View>
        </ScrollView>
        <Button
          title="Continue"
          filled
          onPress={onChangePassword}
          isLoading={isLoading}
          style={styles.button}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: COLORS.white,
  },
  success: {
    width: SIZES.width * 0.8,
    height: 250,
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 52,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Urbanist Medium',
    color: COLORS.black,
    marginVertical: 12,
  },
  center: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkBoxContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 18,
  },
  checkbox: {
    marginRight: Platform.OS === 'ios' ? 8 : 14,
    height: 16,
    width: 16,
    borderRadius: 4,
    borderColor: COLORS.primary,
    borderWidth: 2,
  },
  privacy: {
    fontSize: 12,
    fontFamily: 'Urbanist Regular',
    color: COLORS.black,
  },
  socialTitle: {
    fontSize: 19.25,
    fontFamily: 'Urbanist Medium',
    color: COLORS.black,
    textAlign: 'center',
    marginVertical: 26,
  },
  socialBtnContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 18,
    position: 'absolute',
    bottom: 12,
    right: 0,
    left: 0,
  },
  bottomLeft: {
    fontSize: 14,
    fontFamily: 'Urbanist Regular',
    color: 'black',
  },
  bottomRight: {
    fontSize: 16,
    fontFamily: 'Urbanist Medium',
    color: COLORS.primary,
  },
  button: {
    marginVertical: 6,
    width: SIZES.width - 32,
    borderRadius: 30,
  },
  forgotPasswordBtnText: {
    fontSize: 16,
    fontFamily: 'Urbanist SemiBold',
    color: COLORS.primary,
    textAlign: 'center',
    marginTop: 12,
  },
  modalTitle: {
    fontSize: 24,
    fontFamily: 'Urbanist Bold',
    color: COLORS.primary,
    textAlign: 'center',
    marginVertical: 12,
  },
  modalSubtitle: {
    fontSize: 16,
    fontFamily: 'Urbanist Regular',
    color: COLORS.greyscale600,
    textAlign: 'center',
    marginVertical: 12,
  },
  modalContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  modalSubContainer: {
    height: 494,
    width: SIZES.width * 0.9,
    backgroundColor: COLORS.white,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  modalIllustration: {
    height: 180,
    width: 180,
    marginVertical: 22,
  },
});

export default CreateNewPassword;
