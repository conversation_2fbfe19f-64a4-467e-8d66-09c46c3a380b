import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {COLORS, SIZES} from '../constants';
import {SafeAreaView} from 'react-native-safe-area-context';
import Header from '../components/Header';
import {useTheme} from '../theme/ThemeProvider';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import Button from '../components/Button';
import {useAppDispatch, useAppSelector} from '../redux/hooks';
import {
  addAmountToWallet,
  getWallet,
  Transaction,
} from '../redux/wallet/walletSlice';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const baseAmount = [
  {id: '1', amount: '10'},
  {id: '2', amount: '20'},
  {id: '3', amount: '50'},
  {id: '4', amount: '100'},
  {id: '5', amount: '200'},
  {id: '6', amount: '250'},
  {id: '7', amount: '500'},
  {id: '8', amount: '750'},
  {id: '9', amount: '1000'},
];

// Topup Amount Screen
const TopupAmount = () => {
  const navigation = useNavigation<NavigationProp<any>>();
  const [selectedAmount, setSelectedAmount] = useState('');
  const {colors, dark} = useTheme();
  const user = useAppSelector(state => state.auth.userData?.userData);
  const token = useAppSelector(state => state.auth.userData?.token);
  const wallet = useAppSelector(state => state.wallet.wallet);
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (token && user && user.id) {
      setLoading(true);
      dispatch(getWallet({token: token, userId: user.id})).finally(() => {
        setLoading(false);
      });
    }
  }, [dispatch, token, user]);

  const handleAmountSelection = (amount: any) => {
    setSelectedAmount(amount);
  };

  const onAddToWallet = () => {
    if (!selectedAmount) {
      Alert.alert('Error', 'Please enter amount first.');
      return;
    }
    if (user && user.id) {
      setLoading(true);
      dispatch(
        addAmountToWallet({
          token: token,
          userId: user.id,
          amount: selectedAmount,
          description: 'Added from mobile app.',
        }),
      ).finally(() => {
        dispatch(getWallet({token: token, userId: user.id})).finally(() => {
          setSelectedAmount('');
          setLoading(false);
        });
      });
    } else {
      Alert.alert('Error', 'Something went wrong!');
    }
    // navigation.navigate('topupmethods');
  };

  // Render each transaction
  const renderTransaction = ({item}: {item: Transaction}) => {
    // Format date for better readability (e.g., "25 May 2025")
    const formattedDate = new Date(item.createdAt).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });

    // Determine icon and color based on transaction type
    const isCredit = item.type === 'credit';
    const iconName = isCredit ? 'arrow-up-circle' : 'arrow-down-circle';
    const amountColor = isCredit ? COLORS.primary : COLORS.red;
    const amountPrefix = isCredit ? '+' : '-';

    return (
      <View
        style={[
          styles.transactionContainer,
          {backgroundColor: dark ? COLORS.dark2 : COLORS.white},
        ]}>
        <View style={styles.transactionRow}>
          {/* Icon for transaction type */}
          <Icon
            name={iconName}
            size={24}
            color={amountColor}
            style={styles.transactionIcon}
          />
          <View style={styles.transactionDetails}>
            <Text style={[styles.transactionAmount, {color: amountColor}]}>
              {amountPrefix} {item.amount} AED
            </Text>
            <Text style={[styles.transactionDescription, {color: colors.text}]}>
              {item.description}
            </Text>
            <Text style={[styles.transactionDate, {color: colors.text}]}>
              {formattedDate}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.area, {backgroundColor: colors.background}]}>
      <View style={[styles.container, {backgroundColor: colors.background}]}>
        <Header title="E-wallet" />
        <View style={styles.contentContainer}>
          <Text style={[styles.yourBalance, {color: colors.text}]}>
            Your Balance: {wallet?.balance || '0'} AED
          </Text>
          <TextInput
            placeholder="Enter the amount in AED"
            style={[
              styles.input,
              {
                color: COLORS.primary,
                borderColor: dark ? COLORS.white : COLORS.primary,
              },
            ]}
            value={selectedAmount}
            onChangeText={setSelectedAmount}
          />
          <FlatList
            data={baseAmount}
            keyExtractor={item => item.id}
            numColumns={3}
            columnWrapperStyle={{gap: 4}}
            style={styles.amountList}
            renderItem={({item}) => (
              <TouchableOpacity
                style={[
                  styles.amountContainer,
                  {
                    borderColor: dark ? COLORS.white : COLORS.primary,
                  },
                ]}
                onPress={() => handleAmountSelection(item.amount)}>
                <Text
                  style={[
                    styles.amount,
                    {
                      color: dark ? COLORS.white : COLORS.primary,
                    },
                  ]}>
                  {item.amount}
                </Text>
              </TouchableOpacity>
            )}
          />
          <Button title="Add to wallet" filled onPress={onAddToWallet} />
          {/* Transactions Section */}
          {wallet?.transactions && wallet.transactions.length > 0 ? (
            <View style={styles.transactionsSection}>
              <Text style={[styles.transactionsTitle, {color: colors.text}]}>
                Recent Transactions
              </Text>
              <FlatList
                data={wallet.transactions}
                keyExtractor={item => item.id.toString()}
                renderItem={renderTransaction}
                style={styles.transactionsList}
                nestedScrollEnabled={true}
                showsVerticalScrollIndicator={false}
              />
            </View>
          ) : (
            <Text style={[styles.noTransactions, {color: colors.text}]}>
              No transactions available
            </Text>
          )}
        </View>
      </View>
      {loading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator color={COLORS.primary} size={'large'} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  area: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    padding: 16,
  },
  contentContainer: {
    flex: 1,
  },
  yourBalance: {
    color: COLORS.primary,
    fontSize: 24,
    marginTop: 20,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  input: {
    width: SIZES.width - 32,
    borderRadius: 32,
    paddingVertical: 20,
    color: COLORS.primary,
    borderWidth: 2,
    borderColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 18,
    fontFamily: 'Urbanist ExtraBold',
    textAlign: 'center',
  },
  amountList: {
    marginVertical: 22,
    maxHeight: 150, // Limit height to prevent overflow
  },
  amountContainer: {
    width: (SIZES.width - 48) / 3,
    height: 42,
    borderRadius: 36,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: COLORS.primary,
    borderWidth: 2,
    marginBottom: 12,
  },
  amount: {
    fontSize: 16,
    fontFamily: 'Urbanist Bold',
    color: COLORS.primary,
    textAlign: 'center',
  },
  transactionsSection: {
    marginTop: 20,
    flex: 1, // Allow transactions section to take remaining space
  },
  transactionsTitle: {
    fontSize: 20,
    fontFamily: 'Urbanist Bold',
    marginBottom: 10,
  },
  transactionsList: {
    maxHeight: 300, // Constrain height for independent scrolling
  },
  transactionContainer: {
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: COLORS.gray,
    shadowColor: COLORS.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  transactionRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionIcon: {
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontFamily: 'Urbanist Regular',
    marginBottom: 4,
  },
  transactionAmount: {
    fontSize: 16,
    fontFamily: 'Urbanist SemiBold',
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 14,
    fontFamily: 'Urbanist Regular',
    color: COLORS.gray,
  },
  noTransactions: {
    fontSize: 16,
    fontFamily: 'Urbanist Regular',
    textAlign: 'center',
    marginTop: 20,
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    zIndex: 100,
  },
});

export default TopupAmount;
